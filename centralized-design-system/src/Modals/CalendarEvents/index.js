import { tr } from 'edc-web-sdk/helpers/translations';
import React, { useState, useEffect, useRef } from 'react';
import { bool, func } from 'prop-types';
import { getCards } from 'edc-web-sdk/requests/cards';
import { getMKPCourseEvents, getMKPCourseECLID } from 'edc-web-sdk/requests/mkpCourseEvents';

import { translatr } from '../../Translatr';
import Modal, { ModalHeader, ModalContent } from '../index';
import Loading from '../../Loading';
import restrictTabNavigation from '../../Utils/restrictTabNavigation';

import './styles.scss';

const dateFormatOptions = {
  hour: 'numeric',
  minute: 'numeric'
};

const CalendarEvents = ({ show = false, callback = () => {} }) => {
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [calendarDay, setCalendarDay] = useState(); // Future use
  const [calendarMonth, setCalendarMonth] = useState();
  const [monthDisplayName, setMonthDisplayName] = useState();
  const [calendarYear, setCalendarYear] = useState();
  const [daysArray, setDaysArray] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState({});
  const [openPopovers, setOpenPopovers] = useState(new Set());
  const [triggeringElements, setTriggeringElements] = useState(new Map());
  const datesMap = useRef({});

  useEffect(() => {
    const restrictionContainer = document.getElementsByClassName('ed-dialog-modal')[0];

    if (!open || loading || !restrictionContainer) return;

    const tabNavigatableElements = [
      ...Array.from(restrictionContainer.querySelectorAll('.ed-dialog-modal-header-close-button')), // close button
      ...Array.from(restrictionContainer.querySelectorAll('.calendar-nav button')), // navigation buttons
      ...Array.from(restrictionContainer.querySelectorAll('.cal-day > .cal-event')).slice(-2) // last 2 event entries
    ];

    const handleKeydownFn = restrictTabNavigation(restrictionContainer, tabNavigatableElements);

    return () => restrictionContainer.removeEventListener('keydown', handleKeydownFn);
  }, [open, loading]);

  useEffect(() => {
    setOpen(show);
    if (show && Object.keys(calendarEvents).length !== 0) {
      setLoading(false);
    }
  }, [show]);

  useEffect(() => {
    if (open) {
      setMonth();
      getData();
    }
  }, [open]);

  async function getData(append = false, month = null, year = null) {
    // https://edqa.cmnetwork.co/api/v2/cards.json?card_type[]=training&card_type[]=course&card_subtype[]=vilt&card_subtype[]=mkp_ilt&fields=message,id,share_url,training_details&limit=10
    const payload = {
      'card_type[]': ['training'],
      'card_subtype[]': ['vilt'],
      offset: 0,
      limit: 100,
      fields: 'message,id,share_url,training_details,card_subtype,ecl_id'
    };

    let allData = [];
    let data = [];
    let totalCount = 0;

    if (!append) {
      let resp = await getCards(payload, true).catch(err => {
        console.error('Error CalendarEvents.getData.func', err);
      });

      totalCount = resp.total;
      data = [...resp.cards];

      let queryAll = [];
      let reqCount = data.length;

      while (reqCount < totalCount) {
        queryAll.push(
          new Promise(async resolve => {
            const nPayload = { ...payload };
            nPayload.offset = reqCount;
            let res = await getCards(nPayload, true).catch(err => {
              console.error('Error in CalendarEvents.queryAll.map', err);
              resolve([]); // Ignore and continue on
            });
            resolve(res?.cards);
          })
        );
        reqCount += 100;
      }

      let resAll = await Promise.all(queryAll);
      let resItem;

      resAll.forEach(r => {
        resItem = typeof r !== 'undefined' ? r : [];
        data = [...data, ...resItem];
      });

      data.forEach(event => {
        if (event.cardSubtype === 'vilt' && event.trainingDetails?.startDate) {
          allData.push(event);
        }
      });
    }

    let dates = getMonthStartAndEnd(month, year);
    let calendarMap = {};
    if (append) {
      // Check to see if the "month" has already been grabbed
      if (datesMap.current[dates.startDate]) {
        return;
      } else {
        setLoading(true);
        datesMap.current[dates.startDate] = true;
        calendarMap = JSON.parse(JSON.stringify(calendarEvents));
      }
    }

    let mkpResp = [];

    // Adding a catch in case endpoint doesn't exist
    try {
      mkpResp = await getMKPCourseEvents({
        from: dates.startDate,
        till: dates.endDate
      }).catch(err => {
        console.error('Error CalendarEvents.getData.mkpResp.func', err);
      });
    } catch (err) {
      console.error('Error in CalendarEvents.getMKPCourseEvents.try.func', err);
    }

    let mkpData = [];

    if (mkpResp?.body?.data) {
      Object.keys(mkpResp.body.data).forEach(date => {
        mkpData = [...mkpData, ...mkpResp.body.data[date]];
      });
    }

    mkpData.forEach(event => {
      event.trainingDetails = {
        startDate: event.startDate,
        endDate: event.endDate,
        registrations: {
          state: event.enrollmentStatus?.configKey || ''
        }
      };
      allData.push(event);
    });

    // Now loop and create a map of all events
    allData.forEach(event => {
      let shareUrl = event.shareUrl;

      if (!shareUrl && event.cardDetails?.eclId) {
        shareUrl = `/insights/ECL-${event.cardDetails.eclId}`;
      }
      let eventData = {
        id: event.id,
        title: event.message || event.title,
        url: shareUrl,
        sku: event.masterSKU // MKP item only
      };
      if (event.trainingDetails?.startDate) {
        let startDate = new Date(event.trainingDetails.startDate);
        let endDate = new Date(event.trainingDetails.endDate);
        let registration = event.trainingDetails.registrations?.state || '';

        eventData.startDate = startDate;
        eventData.endDate = endDate;
        eventData.registration = registration;

        let calendarKey = `${startDate.getFullYear()}-${startDate.getMonth() +
          1}-${startDate.getDate()}`;
        if (!calendarMap[calendarKey]) {
          calendarMap[calendarKey] = [];
        }
        calendarMap[calendarKey].push(eventData);
      }
    });
    setCalendarEvents(calendarMap);
    setLoading(false);
  }

  function getMonthStartAndEnd(monthToSet = null, yearToSet = null) {
    const d = new Date(),
      m = monthToSet || d.getMonth() + 1, // current month or set a new month
      y = yearToSet || d.getFullYear(),
      date = new Date(y, m - 1, 1),
      month = date.getMonth(),
      year = date.getFullYear(),
      first = new Date(year, month, 1), // First day of current month
      last = new Date(year, month + 1, 0); // Last day of current month
    let startDate = first.toISOString().split('T')[0];
    let endDate = last.toISOString().split('T')[0];
    return {
      startDate,
      endDate
    };
  }

  // monthToSet is the calendar month, Jan = 1, Dec = 12
  // yearToSet is a 4 digit year
  function setMonth(monthToSet, yearToSet) {
    const d = new Date(),
      m = monthToSet || d.getMonth() + 1, // current month or set a new month
      y = yearToSet || d.getFullYear(),
      date = new Date(y, m - 1, 1),
      month = date.getMonth(),
      year = date.getFullYear(),
      first = new Date(year, month, 1), // First day of current month
      last = new Date(year, month + 1, 0), // Last day of current month
      startingDay = first.getDay(), // Monday, Tuesday, etc...
      previousMonthLastDay = new Date(year, month, 0); //28,29,30,31
    setMonthDisplayName(date.toLocaleString(navigator.language, { month: 'long' }));
    setCalendarDay(date);
    setCalendarMonth(m);
    setCalendarYear(y);

    let days = [];
    let dayIndex = 0; //startingDay;

    // Fill in previous month
    let previousDays = 7 - (7 - startingDay);
    let previousStartDay = previousMonthLastDay.getDate() - (previousDays - 1);
    for (let i = previousStartDay; i <= previousMonthLastDay.getDate(); i++) {
      days[dayIndex] = i;
      dayIndex++;
    }

    // Fill in current month
    for (let i = 0; i < last.getDate(); i++) {
      days[dayIndex] = i + 1;
      dayIndex++;
    }

    // If dayIndex < 5 weeks x 7 days , then Array will be 35, otherwise 6 weeks x 7days
    // Fill in any remaining days
    let remainingDays = dayIndex < 36 ? 35 - dayIndex : 42 - dayIndex;
    for (let i = 0; i < remainingDays; i++) {
      days[dayIndex] = i + 1;
      dayIndex++;
    }

    setDaysArray(days);
  }

  function closeModal() {
    closeAllPopovers(); // Close any open popovers when modal closes
    setOpen(false);
    callback(false);
  }

  // Helper functions for popover management
  function openPopover(eventId, triggeringElement) {
    setOpenPopovers(prev => new Set([...prev, eventId]));
    setTriggeringElements(prev => new Map([...prev, [eventId, triggeringElement]]));
  }

  function closePopover(eventId) {
    setOpenPopovers(prev => {
      const newSet = new Set(prev);
      newSet.delete(eventId);
      return newSet;
    });

    // Return focus to triggering element
    const triggeringElement = triggeringElements.get(eventId);
    if (triggeringElement) {
      triggeringElement.focus();
      setTriggeringElements(prev => {
        const newMap = new Map(prev);
        newMap.delete(eventId);
        return newMap;
      });
    }
  }

  function closeAllPopovers() {
    setOpenPopovers(new Set());
    setTriggeringElements(new Map());
  }

  // ESC key handler for closing popovers
  useEffect(() => {
    function handleEscKey(event) {
      if (event.key === 'Escape' && openPopovers.size > 0) {
        // Close the most recently opened popover
        const lastOpenedId = Array.from(openPopovers).pop();
        if (lastOpenedId) {
          closePopover(lastOpenedId);
        }
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEscKey);
      return () => document.removeEventListener('keydown', handleEscKey);
    }
  }, [open, openPopovers, triggeringElements]);

  function goToPreviousMonth() {
    let currentDate = calendarDay;
    let previousMonth = new Date(currentDate.setMonth(currentDate.getMonth() - 1));
    setMonth(previousMonth.getMonth() + 1, previousMonth.getFullYear());
    getData(true, previousMonth.getMonth() + 1, previousMonth.getFullYear());
  }

  function goToNextMonth() {
    let currentDate = calendarDay;
    let nextMonth = new Date(currentDate.setMonth(currentDate.getMonth() + 1));
    setMonth(nextMonth.getMonth() + 1, nextMonth.getFullYear());
    getData(true, nextMonth.getMonth() + 1, nextMonth.getFullYear());
  }

  if (!open) {
    return null;
  }

  function renderRemainingEvents(events, day) {
    return events.map(event => {
      let startDate = event.startDate instanceof Date ? event.startDate : new Date(event.startDate);
      let endDate = event.endDate instanceof Date ? event.endDate : new Date(event.endDate);
      let dateStamp = new Intl.DateTimeFormat(navigator.language).format(startDate);
      let startTime = new Intl.DateTimeFormat(navigator.language, dateFormatOptions).format(
        startDate
      );
      let endTime = new Intl.DateTimeFormat(navigator.language, dateFormatOptions).format(endDate);
      let EnrolledOnActive =
        event.registration == 'active' ? `${translatr('cds.common.main', 'EnrolledOn')}-` : '';

      const headerEventId = `header-${event.id}`;
      const isPopoverOpen = openPopovers.has(headerEventId);

      return (
        <button
          key={event.id}
          onClick={e => handleModalCalEventShow(e, event.id)}
          onKeyDown={e => handleModalCalEventShow(e, event.id)}
          aria-label={`${EnrolledOnActive}${event.title} on ${day} ${monthDisplayName} `}
          aria-expanded={isPopoverOpen}
          className={`cal-event ${event.registration}`}
        >
          <span>{event.title}</span>
          {isPopoverOpen && (
            <div
              role="dialog"
              aria-labelledby={`event-title-${event.id}`}
              className="cal-event__modal"
            >
              <button
                className="cal-event__modal-close"
                onClick={(e) => {
                  e.stopPropagation();
                  closePopover(headerEventId);
                }}
                aria-label={translatr('cds.common.main', 'Close')}
              >
                ×
              </button>
              <h5 id={`event-title-${event.id}`}>{event.title}</h5>
              <p>
                {dateStamp} {startTime} - {endTime}
              </p>
              <br />
              <a href={event.url} target="_blank" rel="noopener noreferrer">
                {translatr('cds.common.main', 'ViewMeetingDetails')}
                <span className="sr-only">
                  {translatr('cds.common.main', 'ClickingThisLinkWillOpenTheDetailsInANewTab')}
                </span>
              </a>
            </div>
          )}
        </button>
      );
    });
  }

  async function handleMkpClick(event) {
    if (event.url) {
      return;
    }
    if (event.sku) {
      let resp = await getMKPCourseECLID({ 'course_skus[]': event.sku }).catch(err => {
        console.error('Error in CalendarEvents.handleMkpClick.func: ', err);
      });

      if (resp?.body?.[0]) {
        window.open(`/insights/ECL-${resp.body[0].ecl_id}`, '_blank');
      }
    }
  }

  const handleModalShow = (e, id) => {
    e.stopPropagation();
    if (e.key === 'Enter' || e.type === 'click') {
      if (openPopovers.has(id)) {
        closePopover(id);
      } else {
        openPopover(id, e.currentTarget);
      }
    }
  };

  const handleModalMoreShow = (e, id) => {
    e.stopPropagation();
    if (e.key === 'Enter' || e.type === 'click') {
      const moreViewId = `more-view-${id}`;
      if (openPopovers.has(moreViewId)) {
        closePopover(moreViewId);
      } else {
        openPopover(moreViewId, e.currentTarget);
      }
    }
  };

  const handleModalCalEventShow = (e, id) => {
    e.stopPropagation();
    if (e.key === 'Enter' || e.type === 'click') {
      const headerEventId = `header-${id}`;
      if (openPopovers.has(headerEventId)) {
        closePopover(headerEventId);
      } else {
        openPopover(headerEventId, e.currentTarget);
      }
    }
  };

  const modalHeaderId = 'cal-modal-header';

  return (
    <Modal className="ed-calendar-events" ariaLabelledBy={modalHeaderId}>
      <ModalHeader
        id={modalHeaderId}
        title={translatr('cds.common.main', 'Calendar')}
        onClose={closeModal}
      />
      <ModalContent>
        <div className="calendar-nav">
          <div className="calendar-nav__month">
            <button
              className="icon-angle-left-arrow leftArrowRTL s-margin-right"
              onClick={goToPreviousMonth}
              aria-label={translatr('cds.common.main', 'Previous')}
            />
            <span>
              {monthDisplayName} {calendarYear}
            </span>
            <button
              className="icon-angle-right-arrow rightArrowRTL s-margin-left"
              onClick={goToNextMonth}
              aria-label={translatr('cds.common.main', 'Next')}
            />
          </div>
        </div>
        <div className="calendar-grid weekdays">
          <div>S</div>
          <div>M</div>
          <div>T</div>
          <div>W</div>
          <div>T</div>
          <div>F</div>
          <div>S</div>
        </div>
        <div className="calendar-grid">
          {daysArray.map((day, index) => {
            let isBeforeOrAfterCurrentMonth = false;
            if (day > index + 1) {
              isBeforeOrAfterCurrentMonth = true;
            }
            if (day < 7 && index > 28) {
              isBeforeOrAfterCurrentMonth = true;
            }
            let meeting = null;

            let calendarKey = `${calendarYear}-${calendarMonth}-${day}`;
            if (!isBeforeOrAfterCurrentMonth && calendarEvents[calendarKey]) {
              let meetingLength = calendarEvents[calendarKey]?.length || 0;
              meeting = calendarEvents[calendarKey]?.map?.((event, index1) => {
                let startDate =
                  event.startDate instanceof Date ? event.startDate : new Date(event.startDate);
                let endDate =
                  event.endDate instanceof Date ? event.endDate : new Date(event.endDate);
                let dateStamp = new Intl.DateTimeFormat(navigator.language).format(startDate);
                let startTime = new Intl.DateTimeFormat(
                  navigator.language,
                  dateFormatOptions
                ).format(startDate);
                let endTime = new Intl.DateTimeFormat(navigator.language, dateFormatOptions).format(
                  endDate
                );
                let today = new Date(`${monthDisplayName} ${day} ${calendarYear}`);
                let dayName = today.toLocaleDateString(navigator.language, { weekday: 'long' });
                let EnrolledOnActive =
                  event.registration == 'active'
                    ? `${translatr('cds.common.main', 'EnrolledOn')}-`
                    : '';

                if (index1 < 2) {
                  const isPopoverOpen = openPopovers.has(event.id);

                  return (
                    <button
                      onClick={e => handleModalShow(e, event.id)}
                      onKeyDown={e => handleModalShow(e, event.id)}
                      aria-label={`${EnrolledOnActive}${event.title} on ${day} ${monthDisplayName}`}
                      aria-expanded={isPopoverOpen}
                      key={event.id}
                      className={`cal-event ${event.registration}`}
                    >
                      <span>{event.title}</span>
                      {isPopoverOpen && (
                        <div
                          role="dialog"
                          aria-labelledby={`main-event-title-${event.id}`}
                          className="cal-event__modal"
                        >
                          <button
                            className="cal-event__modal-close"
                            onClick={(e) => {
                              e.stopPropagation();
                              closePopover(event.id);
                            }}
                            aria-label={translatr('cds.common.main', 'Close')}
                          >
                            ×
                          </button>
                          <h5 id={`main-event-title-${event.id}`}>{event.title}</h5>
                          <p>
                            {dateStamp} {startTime} - {endTime}
                          </p>
                          <br />
                          <a
                            href={event.url || null}
                            onClick={() => handleMkpClick(event)}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {translatr('cds.common.main', 'ViewMeetingDetails')}
                            <span className="sr-only">
                              {translatr(
                                'cds.common.main',
                                'ClickingThisLinkWillOpenTheDetailsInANewTab'
                              )}
                            </span>
                          </a>
                        </div>
                      )}
                    </button>
                  );
                } else if (index1 === 2) {
                  const moreViewId = `more-view-${event.id}`;
                  const isMorePopoverOpen = openPopovers.has(moreViewId);

                  return (
                    <div
                      key={`cal-event-view-more-${calendarKey}`}
                      className="cal-event event-view-more"
                      role="button"
                      tabIndex="0"
                      onClick={e => handleModalMoreShow(e, event.id)}
                      onKeyDown={e => handleModalMoreShow(e, event.id)}
                      aria-expanded={isMorePopoverOpen}
                      aria-label={`View ${meetingLength - 2} ${translatr(
                        'cds.common.main',
                        'More'
                      )} events`}
                    >
                      <span>
                        {meetingLength - 2} {translatr('cds.common.main', 'More')}
                      </span>
                      {isMorePopoverOpen && (
                        <div
                          role="dialog"
                          aria-labelledby={`more-view-header-${event.id}`}
                          className="cal-event__modal modal-view-more"
                        >
                          <button
                            className="cal-event__modal-close"
                            onClick={(e) => {
                              e.stopPropagation();
                              closePopover(moreViewId);
                            }}
                            aria-label={translatr('cds.common.main', 'Close')}
                          >
                            ×
                          </button>
                          <div className="modal-view-more__header" id={`more-view-header-${event.id}`}>
                            {dayName}
                            <br />
                            <strong>{day}</strong>
                          </div>
                          {renderRemainingEvents(calendarEvents[calendarKey], day)}
                        </div>
                      )}
                    </div>
                  );
                } else {
                  return null;
                }
              });
            }

            return (
              <div
                key={`day-${day}-${index}`}
                className={`cal-day ${isBeforeOrAfterCurrentMonth ? 'previous' : 'current'}`}
              >
                <span className="cal-day__date" aria-label={day}>
                  {day}
                </span>
                {meeting}
              </div>
            );
          })}
        </div>
        {loading && <Loading />}
      </ModalContent>
    </Modal>
  );
};

CalendarEvents.propTypes = {
  show: bool,
  callback: func
};
export default CalendarEvents;
