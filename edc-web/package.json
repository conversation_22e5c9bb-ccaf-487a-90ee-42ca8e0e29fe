{"name": "edc-web", "version": "0.1.0", "description": "EdCast web application", "main": "npm start", "scripts": {"start": "echo 'Please use npm run dev'", "dev": "npm-run-all dev:update --parallel dev:webpack dev:server", "dev-light": "npm-run-all --parallel dev:webpack dev:server", "dev-light:win": "npm-run-all --parallel dev:webpack dev:server:win", "dev:update": "npm update edc-web-sdk && npm update centralized-design-system && npm update edc-blocks", "dev:webpack": "webpack-dev-server --color --hot --config webpack.config.js", "dev:server": "NODE_ENV=development node app.js", "dev:server:win": "node app.js", "build:prod": "node --max_old_space_size=4096 node_modules/webpack/bin/webpack.js --progress --color --config webpack.production.config.js", "build:qa": "node node_modules/webpack/bin/webpack.js --progress --color --config webpack.production.config.js", "clean:build": "rm -rf public/dist* && rm -rf version.json", "build:version": "node server/version.js", "build:update": "npm install", "build": "NODE_ENV=production run-s clean:build build:version build:prod", "light-build": "NODE_ENV=production run-s clean:build build:version build:prod", "build-qa": "NODE_ENV=production run-s build:update clean:build build:version build:qa", "cy:run": "ELECTRON_ENABLE_LOGGING=1 cypress run && cypress run --component", "runci": "WEBPACK_MINIMIZE=false npm run build && CI=jenkins NODE_ENV=production EDC_TRANSLATIONS_DOMAIN=resources.edcastqa.io node app.js", "test": "start-server-and-test runci http-get://127.0.0.1:8080 cy:run", "lint": "eslint src --ext js,jsx", "proxy_session": "./tools/proxy_session.sh", "postinstall": "cd server && npm install"}, "author": "ypling", "husky": {"hooks": {"pre-commit": "lint-staged --config=.lintstagedrc.js"}}, "overrides": {"react-rte": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "recharts": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "ua-parser-js": "^1.0.40", "braces": "^3.0.3", "cross-spawn": "^7.0.5", "crypto-js": "^4.2.0", "d3-color": "^3.1.0", "elliptic": "^6.6.1", "fast-xml-parser": "^4.2.4", "ip": "^1.1.9", "marked": "^4.0.10", "path-to-regexp": "^1.9.0", "webpack-dev-middleware": "^5.3.3", "ws": "^8.17.1", "readable-stream": "^4.7.0"}, "dependencies": {"@lego/elements-galaxy": "^15.0.0-build.37", "@mui/material": "^5.15.7", "agora-rtc-sdk": "^3.6.11", "agora-rtm-sdk": "^1.5.1", "babel-polyfill": "^6.26.0", "blurhash": "^2.0.5", "buffer": "^6.0.3", "centralized-design-system": "git+ssh://********************************:7999/ent-lx/centralized-design-system#master", "classnames": "^2.5.1", "compression-webpack-plugin": "^11.1.0", "create-react-class": "^15.7.0", "crypto-browserify": "^3.12.0", "css-loader": "^7.1.2", "d3-force": "^3.0.0", "dompurify": "^3.2.4", "dotenv": "^8.2.0", "edc-blocks": "git+ssh://********************************:7999/ent-lx/edc-blocks#main", "edc-web-sdk": "git+ssh://********************************:7999/ent-lx/edc-web-sdk#master", "esbuild-loader": "^4.3.0", "foundation-sites": "^6.8.1", "glob": "^7.1.2", "global": "^4.4.0", "html-react-parser": "^5.2.2", "js-file-download": "0.4.12", "js-sha256": "^0.11.0", "jsencrypt": "^3.3.2", "ldclient-js": "2.7.0", "locale-currency": "0.0.2", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.8.0", "moment": "2.30.1", "moment-timezone": "0.5.45", "pdfjs-dist": "4.8.69", "postcss": "^8.4.41", "postcss-loader": "^8.1.1", "postcss-rtl": "^2.0.0", "prop-types": "^15.8.1", "rc-scrollbars": "^1.1.6", "react": "^18.2.0", "react-blurhash": "^0.3.0", "react-bootstrap-typeahead": "^6.3.2", "react-csv": "^1.0.14", "react-dom": "^18.2.0", "react-dropdown-tree-select": "^2.8.0", "react-flatpickr": "^3.10.13", "react-focus-lock": "^2.9.7", "react-image-crop": "^3.0.11", "react-linkify": "^0.2.2", "react-loadable": "5.5.0", "react-player": "^2.14.1", "react-redux": "8.0.5", "react-router-dom": "^6.22.1", "react-rte": "^0.16.5", "react-select": "^5.8.0", "react-slick": "^0.30.1", "react-slider": "^2.0.6", "react-sortablejs": "^6.1.4", "react-star-ratings": "^2.3.0", "react-text-mask": "5.5.0", "react-transition-group": "^2.2.1", "react-window": "^1.8.10", "recharts": "^2.15.1", "sass": "^1.70.0", "sass-loader": "^13.3.2", "simple-crypto-js": "^3.0.1", "slick-carousel": "1.8.1", "sortablejs": "^1.15.2", "speed-measure-webpack-plugin": "^1.5.0", "stream-browserify": "^3.0.0", "styled-components": "^6.1.8", "superagent": "^3.4.x", "timers-browserify": "^2.0.12", "unsplash-js": "^7.0.19", "url-search-params-polyfill": "^4.0.0", "webpack": "^5.90.1", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^6.0.1", "whatwg-fetch": "^2.0.3"}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.23.9", "@babel/eslint-parser": "^7.27.0", "@babel/node": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-sent": "^7.23.3", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-throw-expressions": "^7.23.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-parameters": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.3", "@babel/register": "^7.23.7", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "cypress": "^14.2.1", "cypress-terminal-report": "^7.0.2", "del": "^7.1.0", "eslint": "^8.56.0", "eslint-plugin-cypress": "^3.4.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-sonarjs": "^0.23.0", "eslint-webpack-plugin": "^4.0.1", "file-loader": "^6.2.0", "glob": "^7.1.2", "husky": "^1.3.1", "immutable": "^3.8.1", "lint-staged": "^15.2.2", "npm-run-all": "^4.1.5", "prettier": "^1.16.4", "react-window-infinite-loader": "^1.0.9", "redux": "4.x.x", "redux-logger": "^3.0.6", "redux-thunk": "2.x.x", "start-server-and-test": "^2.0.11", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack-dev-server": "^5.2.1"}}