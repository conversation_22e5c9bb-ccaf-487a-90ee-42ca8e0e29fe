export const FEED_MAP = {
  'feed/todaysLearning': '/feed/todays-learning-search',
  'feed/todaysLearningSearch': '/feed/new-todays-learning-search',
  'feed/teamLearning': '/feed/team-learning',
  'feed/featured': '/feed/featured',
  'feed/recommended': '/feed/recommended',
  'feed/recommendations': '/feed/recommendations',
  'feed/myAssignments': '/feed/my-assignments',
  'feed/curate': '/feed/curate',
  'feed/submission': '/feed/submission'
};

export const CARD_LAYOUT = {
  TILE: 'tile',
  BIG_CARD: 'bigcard',
  STANDALONE: 'standalone',
  LIST_VIEW: 'listview'
};

export const LOWERCASE_COURSE = 'course';
export const LOWERCASE_ASSESSMENT = 'assessment';
export const LOWERCASE_PROCTORED_ASSESSMENT = 'proctored_assessment';
export const LOWERCASE_COMPLETED = 'completed';

// Badge character limit
export const BADGE_CHAR_LIMIT = 50;

// EdChatBotUrl
export const DEFAULT_ED_CHAT_BOT_URL = 'https://chatbot-qa.cmnetwork.co/';

//date-time format
export const DAY_MONTH_YEAR_WITH_LEADING_ZERO_FORMAT = 'DD MMM YYYY';
export const MONTH_DAY_YEAR_FORMAT = 'MM-DD-YYYY';
export const TIMESTAMP_WITH_SECONDS_FORMAT = 'YYYY-MM-DD hh:mm:ss A';
export const DAY_MONTH_YEAR_FORMAT = 'DD/MM/YYYY';
export const MONTH_DAY_YEAR_SLASH_FORMAT = 'MM/DD/YYYY';
export const FORMATTED_DATE_PATTERN = 'DD MMM, YYYY';
export const MONTH_AND_YEAR_FORMAT = 'MMM YYYY';

// eclSourceTypeName
export const SOCIATIVE_V2 = 'sociative_v2';

export const IN_PROGRESS_STATUS = 'In Progress';
export const COMPLETION_STATE = ['INITIALIZED', 'COMPLETED', 'STARTED'];

export const NO_LEVEL = 'no_level';

// Access Denied
export const IP_ADDRESS_NOT_ALLOWED = 'Your IP Address is not allowed';

export const DURATION_LABEL = {
  ZERO_TO_TEN: 'ZeroTo10Min',
  TEN_TO_ONE_HR: 'TenMinTo1Hr',
  ONE_TO_SIX_HR: 'OneHrTo6Hr',
  SIX_TO_TWENTYFOUR_HR: 'SixHrTo24Hr',
  MORE_THAN_TWENTYFOUR: 'MoreThan24Hrs'
};

export const DURATION_CASE = {
  CASE_ZERO_TO_TEN: '0 - 10 min',
  CASE_TEN_TO_ONE_HR: '10 min - 1 hr',
  CASE_ONE_TO_SIX_HR: '1 hr - 6 hr',
  CASE_SIX_TO_TWENTYFOUR_HR: '6 hr - 24 hr',
  CASE_MORE_THAN_TWENTYFOUR: 'more than 24 hrs'
};

export const PATHWAY_AND_JOURNEY_PATHS = ['/pathways/', '/journey/', '/journeys/'];

export const DATE_RANGE = {
  fromDate: 'From Date',
  toDate: 'To Date'
};

export const GALAXY_THEME_PAYLOAD = {
  product_slug: 'lxp',
  service_slug: 'platform',
  feature_slug: 'ui',
  flag_slug: 'enable_new_galaxy_theme'
};
