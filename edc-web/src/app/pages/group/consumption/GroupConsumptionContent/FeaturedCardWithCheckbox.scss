@import '~centralized-design-system/src/Styles/_variables.scss';

.featured-card-with-checkbox {
  border: var(--ed-border-size-sm) solid var(--ed-border-color);
  margin-top: var(--ed-spacing-2xs);
  margin-bottom: var(--ed-spacing-2xs);
  border-radius: var(--ed-border-radius-lg);
  padding: var(--ed-spacing-2xs);

  .checkbox-group label.checkbox {
    padding-left: var(--ed-spacing-2xs);
  }

  li {
    list-style: none;
  }

  .featured__container--card {
    pointer-events: none;
    &:hover {
      box-shadow: none;
    }

    // Enable tooltip functionality even when card has pointer-events: none
    .ed-tooltip.featured-card-tooltip {
      pointer-events: auto !important;

      // Style the tooltip content to indicate it's clickable for checkbox toggle
      cursor: default !important;
      user-select: none !important;

      // Ensure the title area can receive clicks for checkbox toggling
      .info__container--title {
        pointer-events: auto !important;
        cursor: default !important;
      }
    }
  }
  @media screen and (max-width: 372px) {
    padding-bottom: 0.8125rem;
    .featured__container--card {
      padding-right: 0;
    }
    .checkbox input[type='checkbox'] {
      margin-right: 0;
    }
  }
}
