import React from 'react';
import PropTypes from 'prop-types';
import Checkbox from 'centralized-design-system/src/Checkbox';

import FeaturedCard from './FeaturedCard';

import './FeaturedCardWithCheckbox.scss';

const FeaturedCardWithCheckbox = React.memo(({ card, isSelected = false, setSelectedCard }) => {
  const handleChange = e => {
    if (e?.target?.checked) {
      setSelectedCard(selectedCard => [...selectedCard, card]);
    } else {
      setSelectedCard(selectedCard => selectedCard.filter(x => x.id !== card.id));
    }
  };

  return (
    <div className="featured-card-with-checkbox align-items-center">
      <Checkbox
        checked={isSelected}
        name={`checkbox-${card.id}`}
        id={`checkbox-${card.id}`}
        onChange={handleChange}
        value="1"
        label={<FeaturedCard card={card} allowCheckboxToggle={true}></FeaturedCard>}
        isTranslated
      />
    </div>
  );
});

FeaturedCardWithCheckbox.propTypes = {
  card: PropTypes.object,
  isSelected: PropTypes.bool,
  setSelectedCard: PropTypes.func
};

export default FeaturedCardWithCheckbox;
