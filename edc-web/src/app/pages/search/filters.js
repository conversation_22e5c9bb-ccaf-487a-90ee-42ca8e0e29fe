import React, { useState, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getAllSources } from 'edc-web-sdk/requests/ecl';
import { getUsers } from 'edc-web-sdk/requests/users.v2';
import { Select } from 'centralized-design-system/src/Inputs';
import { translatr } from 'centralized-design-system/src/Translatr';

import SearchModal from './SearchModal';
import SingleFilters from './SingleFilters';
import { CHANNEL_SEARCH } from '../channel/all_channel/util';
import { getCachedSourceOrUserIdsFromLocalStorage } from './functions/cachedFiltersFuntions';
import LD from '../../../app/containers/LDStore';
import { CACHED_USER_ID } from '../../../app/constants/localStorageConstants';
import { OFFSET, LIMIT, shouldShowNewSearchUI } from './functions/utils';
import useSkillsFilterPagination from './hooks/useSkillsFilterPagination';
import { saveChannelSearchSources, saveGlobalSearchSources } from '@actions/searchActions';
import { Permissions } from '../../../app/utils/checkPermissions';
import NewSearchAllFilters from './components/NewSearchAllFilters';
import { getSortItems } from './functions/getSortItems';

const publishedAtYearFilterEnabled = LD.enablePublishAtYearFilter();

// using splice to replace Publish date with Created Date
// and maintain ordering of items
if (publishedAtYearFilterEnabled) {
  getSortItems().splice(1, 1, {
    label: translatr('web.search.main', 'SortByPublishedDate'),
    value: 'published_at'
  });
}

const SOURCE_NAMES_TABS = ['card', CHANNEL_SEARCH];

const Filters = ({
  activeTab,
  tabName,
  filterResults,
  toggleFilterModal,
  showFilterModal,
  selectedOptions,
  setSelectedOptions,
  setFilters,
  filters,
  setCallApi,
  sortBy,
  setSortBy,
  contentSources,
  dispatch,
  setShowPublicCards,
  selectedBoxes,
  setSelectedBoxes,
  aggregationsSearch,
  aggregationsData,
  userNamesRef,
  additionalDetails,
  isMultiOrg,
  currentUserLanguageName,
  isNewSearchConfigEnable,
  theme,
  isFlyoutOpen,
  setFlyoutOpen = () => {}
}) => {
  const [userNames, setUserNames] = useState([]);
  const userBucketsIds = useRef();
  const sortItems = useMemo(() => getSortItems(), []);

  const skillsFilterActionObj = useSkillsFilterPagination({
    aggregationsSearch,
    additionalDetails
  });
  const isActiveTabCard = activeTab === 'card';
  const isChannelSearch = activeTab === CHANNEL_SEARCH;

  useEffect(() => {
    if (SOURCE_NAMES_TABS.includes(activeTab)) {
      // Make API call only if sourceNames are not present in store
      if (!contentSources?.length) {
        const payload = {};
        // to pull open source from the sources API only in channel search filter
        if (isChannelSearch) {
          payload.include_open_source_source = true;
        }

        getAllSources(payload)
          .then(response => {
            if (isChannelSearch) {
              dispatch(saveChannelSearchSources(response));
            } else {
              dispatch(saveGlobalSearchSources(response));
            }
          })
          .catch(err => console.error(`Error in Filters: ${err}`));
      }

      // Add user id filter
      const userBuckets = aggregationsSearch['user_id']?.user_id?.buckets;
      userBucketsIds.current = userBuckets?.map(item => item.key);
      const cachedUserIds = getCachedSourceOrUserIdsFromLocalStorage(CACHED_USER_ID);
      const ids =
        cachedUserIds.length && !!cachedUserIds[0] && isActiveTabCard
          ? [...cachedUserIds, ...userBucketsIds.current.filter(id => !cachedUserIds.includes(id))]
          : userBucketsIds.current;

      const user_ids = ids?.slice(0, OFFSET);

      if (ids) {
        const userSearchPayload = {
          limit: LIMIT,
          'user_ids[]': user_ids,
          fields: 'id,full_name',
          ...(isMultiOrg && Permissions.has('ADMIN_ONLY') && { all: true })
        };
        fetchUsers(userSearchPayload);
      }
    }
  }, []);

  const fetchUsers = async payload => {
    try {
      const response = await getUsers(payload);
      if (response) {
        setUserNames(response.items);
        userNamesRef.current = response.items;
      }
    } catch (err) {
      console.error(`Error in Filters for fetching Authors: <AUTHORS>
    }
  };

  const singleFilterProps = {
    activeTab,
    selectedOptions,
    setSelectedOptions,
    setFilters,
    filters,
    setCallApi,
    sourceNames: contentSources,
    selectedBoxes,
    setSelectedBoxes,
    setShowPublicCards,
    aggregationsData,
    tabName,
    currentUserLanguageName,
    isNewSearchConfigEnable
  };

  const searchModalProps = {
    activeTab,
    onClose: toggleFilterModal,
    tabName,
    sourceNames: contentSources,
    userNames,
    userBucketsIds: userBucketsIds.current,
    skillsFilterActionObj,
    filterResults,
    selectedOptions,
    setSelectedOptions,
    filters,
    setFilters,
    setShowPublicCards,
    selectedBoxes,
    setSelectedBoxes,
    aggregationsSearch,
    setCallApi,
    currentUserLanguageName,
    userNamesRef
  };

  const renderNewSearchFilters = () => {
    return (
      <div className="justflex filters-opt-sec flex-column">
        <ul className="filters no-margin justflex align-items-center new-search-filters-wrapper">
          <SingleFilters {...singleFilterProps} />
          <li>
            <NewSearchAllFilters
              {...searchModalProps}
              isFlyoutOpen={isFlyoutOpen}
              setFlyoutOpen={setFlyoutOpen}
            />
          </li>
        </ul>
      </div>
    );
  };

  return !shouldShowNewSearchUI(isNewSearchConfigEnable, activeTab, theme) ? (
    <div className="justflex filters-opt-sec flex-column">
      {isActiveTabCard && (
        <div className="justflex m-margin-bottom flex-end">
          <span className="align-items-center justflex s-margin-right supporting-text">
            {translatr('web.search.main', 'SortBy')}:
          </span>
          <Select
            items={sortItems}
            defaultValue={sortBy || sortItems[0].value}
            onChange={item => setSortBy(item?.value?.trim())}
            translateDropDownOptions={false}
            ariaLabel={translatr('web.search.main', 'SortBy')}
          />
        </div>
      )}
      <ul className="filters flex-end no-margin justflex align-items-center">
        <SingleFilters {...singleFilterProps} />
        <li>
          <button className="all-filters-modal-btn" onClick={toggleFilterModal}>
            <span className="align-items-center justflex pointer remove-margin">
              {translatr('web.search.main', 'AllFilters')}
              <span className="icon-new-filter"></span>
            </span>
          </button>
        </li>
      </ul>
      {showFilterModal && <SearchModal {...searchModalProps} />}
    </div>
  ) : (
    renderNewSearchFilters()
  );
};

const mapStateToProps = ({ team, theme }) => {
  const teamConfig = team?.get('config');
  const isMultiOrg = teamConfig['multi-org'];
  return {
    isMultiOrg,
    theme: theme?.get('themeId')
  };
};

Filters.propTypes = {
  activeTab: PropTypes.string,
  tabName: PropTypes.string,
  sortBy: PropTypes.string,
  filterResults: PropTypes.func,
  toggleFilterModal: PropTypes.func,
  showFilterModal: PropTypes.bool,
  selectedOptions: PropTypes.object,
  filters: PropTypes.object,
  selectedBoxes: PropTypes.object,
  aggregationsSearch: PropTypes.object,
  aggregationsData: PropTypes.object,
  setSelectedOptions: PropTypes.func,
  setSortBy: PropTypes.func,
  contentSources: PropTypes.array,
  dispatch: PropTypes.func,
  setShowPublicCards: PropTypes.func,
  setCallApi: PropTypes.func,
  setFilters: PropTypes.func,
  setSelectedBoxes: PropTypes.func,
  userNamesRef: PropTypes.object,
  additionalDetails: PropTypes.object,
  isMultiOrg: PropTypes.bool,
  currentUserLanguageName: PropTypes.string,
  isNewSearchConfigEnable: PropTypes.bool,
  theme: PropTypes.string,
  isFlyoutOpen: PropTypes.bool,
  setFlyoutOpen: PropTypes.func
};

export default connect(mapStateToProps)(Filters);
