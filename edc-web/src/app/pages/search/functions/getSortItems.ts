import { translatr } from 'centralized-design-system/src/Translatr';

type SortItem = {
  label: string;
  value: string;
};

export const getSortItems = (): SortItem[] => [
  { label: translatr('web.search.main', 'SortByRelevance'), value: 'relevance' },
  { label: translatr('web.search.main', 'SortByCreationDate'), value: 'created_at' },
  { label: translatr('web.search.main', 'SortByDurationHighToLow'), value: 'duration-desc' },
  { label: translatr('web.search.main', 'SortByDurationLowToHigh'), value: 'duration-asc' }
];
