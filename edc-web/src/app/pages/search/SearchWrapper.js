import React, { useState, useEffect, useRef, useMemo } from 'react';
import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { string, func, bool, object, array } from 'prop-types';
import { sendSearchAnalyticsEvent } from 'edc-web-sdk/requests/search';
import { translatr } from 'centralized-design-system/src/Translatr';
import { isEmpty, isEqual, cloneDeep, omit, uniq, concat } from 'lodash';
import { getSkillQueryParamFromUrl } from 'centralized-design-system/src/Header/searchUtils';
import getAccessibleCards from 'centralized-design-system/src/Utils/getAccessibleCards';

import SearchedResults from './searchedResults';
import { getSearchContent } from './functions/getSearchContent';
import Filters from './filters';
import { saveSearchTabState } from '../../../app/actions/searchActions';
import getAggregations from './functions/getAggregations';
import SelectedFilters from './selectedFilters';
import PromotedContent from './PromotedContent';
import { FilterHostname } from '../../../app/components/common/FilterHostname';
import { isSearchFilterApplied } from '../utils';
import updateCounts from './functions/updateCounts';
import { removeFilterQueryParam, updateObjectKeys } from '@utils/utils';
import { getOptionalPayload } from './functions/getOptionalPayload';
import {
  CONTENT_LIBRARY,
  PROFICIENCY_LEVELS_API_KEY,
  PROMOTED_CONTENT_API_LIMIT,
  NO_OF_PROMOTED_CONTENT_TO_SHOW,
  PREFERRED_LANGUAGE,
  extractDisplayNamesFromUrlFilters,
  getTypeFilterKeyValuePairs,
  shouldShowNewSearchUI,
  OpportunityTypes,
  getSearchApiLimit,
  INFO_API_CARDS_BATCH_SIZE,
  INTERNAL_CONTENT_SEARCH_RECURSIVE_THRESHOLD
} from './functions/utils';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import { NO_LEVEL } from '../../../app/utils/constants';
import useFilterNavigation from './hooks/useFilterNavigation';
import { Select } from 'centralized-design-system/src/Inputs';
import ResultsInfo from './components/ResultInfo';
import classNames from 'classnames';
import SearchPeopleGlobal from '../SearchPeopleV2/SearchPeopleGlobal';
import { getFiltersFromSearchUrl } from 'centralized-design-system/src/Header/helper';
import { useEffectAfterInitialMount } from '@utils/hooks';
import { safeDecodeURIComponent } from '@utils/safeDecodeURIComponent';
import SearchOpportunitiesGlobal from '@pages/SearchOpportunities/SearchOpportunitiesGlobal';
import { getCardsInfoData } from './functions/getCardsInfoData';
import LD from '../../../app/containers/LDStore';
import { getSortItems } from './functions/getSortItems';

const filterApiKeys = {
  Type: 'standard_type',
  Languages: 'language',
  'Content Source': 'source_id',
  ContentSource: 'source_id', // Added this because we take state and pass that to url and in url we get ContentSource
  Author: 'user_id',
  Pricing: 'plan',
  'CPE course': 'cpe_content',
  Level: PROFICIENCY_LEVELS_API_KEY,
  Duration: 'duration',
  Provider: 'provider_name',
  'From Date': 'from_date',
  'To Date': 'till_date',
  Skills: 'skill_ids',
  [PREFERRED_LANGUAGE]: PREFERRED_LANGUAGE // This key is not an api key but we just need it to update languages in genrating the paylaod.
};

const isLmsEnabledInstance = isLmsProviderEnabledInstance();

const SearchWrapper = ({
  search,
  activeTab,
  searchTerm,
  advancedFilters,
  tabName,
  tabLabel,
  dispatch,
  isOrgAdmin,
  getActiveLinkUrl,
  languagesReverse,
  setShowPublicCards,
  showPublicCards,
  isSearchSuggestionsEnabled,
  taxonomyDomain,
  currentUserLang,
  isEgtEnabled,
  isOcgEnabled,
  showRecommendedSkills,
  orgConfigs,
  proficiencyLevels,
  theme,
  isNewSearchConfigEnable,
  peopleFilters,
  setPeopleFilters,
  isPromotedContentSearchEnabled
}) => {
  const currentUserLanguageName = useMemo(
    () => Object.entries(languagesReverse).find(ele => ele[0] === currentUserLang)?.[1],
    [currentUserLang]
  );

  const navigate = useNavigate();
  const currentUrlFilters = useMemo(() => getFiltersFromSearchUrl('filters'), [
    window.location.search
  ]);

  const cachedFilters = useMemo(() => {
    if (!currentUrlFilters) return {};

    const decodedFilters = currentUrlFilters.map(([key, values]) => [
      key,
      values.map(value => safeDecodeURIComponent(value))
    ]);

    let filtersObj = decodedFilters.reduce((acc, [key, values]) => {
      if (!acc[key]) {
        acc[key] = [];
      }

      acc[key] = [...new Set([...acc[key], ...values])];

      return acc;
    }, {});

    // Add preferred language only when it's in currentUrlFilters object
    filtersObj[PREFERRED_LANGUAGE] =
      [PREFERRED_LANGUAGE] in filtersObj ? [currentUserLanguageName] : [];

    return filtersObj;
  }, [currentUrlFilters, currentUserLanguageName]);

  const isActiveTabCard = activeTab === 'card';
  const isDefaultSourceEnabled = localStorage.getItem('isDefaultSourceEnabled') === 'true';
  const [searchData, setSearchData] = useState(
    search?.get(activeTab)?.data ? search.get(activeTab).data : {}
  );
  const [aggregationsSearch, setAggregationsSearch] = useState(
    search?.get(activeTab)?.aggregations ? search.get(activeTab).aggregations : {}
  );

  const [isSearchLoading, setIsSearchLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const [showFilterModal, setShowFilterModal] = useState(false);

  // tracks how many cards have already been displayed to the user.
  const [displayedCardCount, setDisplayedCardCount] = useState(0);

  /**
   * extractNamesFromIds is required because we can not save name with id in local state
   * Reason for using the extractNamesFromIds function here during state initialization,
   * rather than during the initialization of cachedFilters, is to preserve cached filters.
   * This is crucial for scenarios where the URL is shared and we don't have content sources
   * and usernames. In such cases, we need to rely on IDs for payload.
   */
  // Specifically used in the API payload when making a search request.
  const [selectedOptions, setSelectedOptions] = useState({
    ...(isActiveTabCard &&
      updateObjectKeys(extractDisplayNamesFromUrlFilters(cachedFilters), filterApiKeys))
  });
  const [totalSearchResults, setTotalSearchResults] = useState(0);
  const [promotedContent, setPromotedContent] = useState(
    search?.get(activeTab)?.cachedPromotedContent ? search.get(activeTab).cachedPromotedContent : []
  );
  const [totalPromotedContent, setTotalPromotedContent] = useState(0);

  // Specifically used in the UI for displaying chips
  const [filters, setFilters] = useState({
    ...(showPublicCards &&
      isActiveTabCard &&
      isDefaultSourceEnabled && { Type: [CONTENT_LIBRARY] }),
    ...(isActiveTabCard && { [PREFERRED_LANGUAGE]: [] }),
    ...(isActiveTabCard && extractDisplayNamesFromUrlFilters(cachedFilters))
  });
  const [callapi, setCallApi] = useState(false);
  // Also used in the UI rendering
  const [selectedBoxes, setSelectedBoxes] = useState({
    ...(showPublicCards &&
      isActiveTabCard &&
      isDefaultSourceEnabled && { Type: [CONTENT_LIBRARY] }),
    ...(isActiveTabCard && { [PREFERRED_LANGUAGE]: [] }),
    ...(isActiveTabCard && extractDisplayNamesFromUrlFilters(cachedFilters))
  });
  const [selectedAggregations, setSelectedAggregations] = useState({});
  const [sortBy, setSortBy] = useState('');
  const [suggestedSearchTerm, setSuggestedSearchTerm] = useState('');
  const [isFlyoutOpen, setFlyoutOpen] = useState(false);

  const offsetRef = useRef(0);
  const promotedContentOffsetRef = useRef(0);
  const userNamesRef = useRef(0);

  // Use cached type keys from URL if aggregations are unavailable (e.g., on reload or shared URL).
  const standardTypeFilterKeyLabels = getTypeFilterKeyValuePairs(
    aggregationsSearch?.['standard_type']?.standard_type?.buckets,
    search?.get('standardTypeFilterKeyLabels'),
    selectedBoxes['Type'],
    cachedFilters['Type']
  );

  const searchStateForTab = search?.get(activeTab);
  const contentTabData = search?.get('card');

  const contentSources = useMemo(() => search?.get('sources')?.get('globalSources'), [search]);

  const { skillId, skillLabel } = getSkillQueryParamFromUrl();

  const searchWithRecommendedSkills = skillId && showRecommendedSkills;

  const sortItems = useMemo(() => getSortItems(), []);

  const enablesBatchLoadingOptimization =
    LD.internalContentSearchLimit() > INTERNAL_CONTENT_SEARCH_RECURSIVE_THRESHOLD &&
    isActiveTabCard &&
    !searchWithRecommendedSkills;

  // Helper function to map items with source ids
  const mapItemsWithSource = ({ sourceArray, nameField, selectedBoxesParam }) => {
    if (!sourceArray?.length) return false;

    // Map the selected items to their corresponding IDs and names
    // Filter out any null value
    return selectedBoxesParam.map(item => {
      const matchedItem = sourceArray.find(obj => obj[nameField] === item);
      // If a match is found, return a string in the format "id|name"
      if (matchedItem) return `${matchedItem['id']}|${matchedItem[nameField]}`;
    });
  };

  const shouldShowNewSearch = shouldShowNewSearchUI(isNewSearchConfigEnable, activeTab, theme);
  /**
   * Transforms the selected filters into a format suitable for generating URL filters.
   *
   * This function is crucial for handling filters like 'Content Source' and 'Author',
   * which need to include IDs separated by a pipe (`|`) (e.g., `"id|name"`).
   *
   * When sharing URLs between users, the second user might not have access to the
   * source IDs and user IDs as they are obtained only after completing search API calls.
   *
   * By transforming the filters into this format, we ensure that when a user shares a URL,
   * the necessary IDs are included in the URL. This allows the second user to apply the
   * same filters accurately, even if they do not have the IDs initially.
   */
  const transformFiltersForUrl = () => {
    if (!isActiveTabCard) return {};

    const _selectedBoxes = cloneDeep(selectedBoxes);
    const urlFilters = {};

    // Select the key for content sources
    const contentSourceKey = _selectedBoxes['Content Source'] ? 'Content Source' : 'ContentSource';
    const authorKey = 'Author';
    const shouldAddContentLibraryFilter =
      isDefaultSourceEnabled && cachedFilters['Type']?.includes([CONTENT_LIBRARY]);

    if (shouldAddContentLibraryFilter) {
      cachedFilters['Type'] = [...(cachedFilters['Type'] || []), [CONTENT_LIBRARY]];
    }
    const updateFiltersInUrl = filterObjKey => {
      switch (filterObjKey) {
        // Required because we need to save id and content source in url
        case contentSourceKey:
          urlFilters[contentSourceKey] = contentSources.length
            ? mapItemsWithSource({
                sourceArray: contentSources,
                nameField: 'display_name',
                selectedBoxesParam: _selectedBoxes[contentSourceKey]
              })
            : cachedFilters[contentSourceKey];
          break;
        // Required because we need to save id and user name in url
        case authorKey:
          urlFilters[authorKey] = userNamesRef.current.length
            ? mapItemsWithSource({
                sourceArray: userNamesRef.current,
                nameField: 'fullName',
                selectedBoxesParam: _selectedBoxes[authorKey]
              })
            : cachedFilters[authorKey];
          break;
        case 'Duration2':
          // Check if 'Duration2' exists in selectedBoxes and replace 'Duration' with it because we save Duratio2 in filters
          if (_selectedBoxes?.['Duration2']) {
            urlFilters['Duration'] = _selectedBoxes['Duration2'];
          }
        case 'Type':
          // Required because we need to save key and label of standard type in url
          urlFilters['Type'] = standardTypeFilterKeyLabels;
          break;
        // Required here because key for CpeCourse is CPE course in filterApiKeys
        case 'CpeCourse':
          urlFilters['CPE course'] = _selectedBoxes['CpeCourse'];
          break;
        default:
          urlFilters[filterObjKey] = _selectedBoxes[filterObjKey];
          break;
      }
    };

    if (isActiveTabCard) {
      const filterKeys = Object.keys(selectedBoxes);
      filterKeys.length > 0 && filterKeys.forEach(filterObjKey => updateFiltersInUrl(filterObjKey));
    }

    // Delete duration2 as we don't need this
    if (urlFilters?.['Duration2']) delete urlFilters['Duration2'];

    // Remove content source in state because we get two content source ; Content Source ContentSource with same values
    // Temporary fix for content source
    // TODO : Remove this code and find out why Content SOurce is duplicated
    return omit(
      {
        ...urlFilters,
        ContentSource: uniq(
          concat(urlFilters.ContentSource || [], urlFilters['Content Source'] || [])
        )
      },
      ['Content Source']
    );
  };

  const { setUrlFilters } = useFilterNavigation(transformFiltersForUrl());

  const additionalDetails = useMemo(
    () => ({
      taxonomyDomain,
      currentUserLang,
      isEgtEnabled,
      isOcgEnabled
    }),
    []
  );

  useEffect(() => {
    // Below if block is applicable if searchsuggestion is enabled
    // It is triggered when user is on any tab other than content and selects skill from the search suggestions
    if (
      isSearchSuggestionsEnabled &&
      activeTab !== 'card' &&
      !!contentTabData &&
      searchTerm !== contentTabData?.searchQuery
    ) {
      dispatch(
        saveSearchTabState('card', {
          ...contentTabData,
          // Cache without filters
          ...{
            selectedSkillId: skillId,
            cardsTabQueryParams: removeFilterQueryParam('filters')
          }
        })
      );
      if (skillId) {
        // return here becuase the presence of skillId indicates that effect triggered as a result of skill click, but the current tab is other than card
        return;
      }
    }

    document.title = FilterHostname(tabLabel);
    setSuggestedSearchTerm('');

    const isEmptyResult = isEmpty(searchStateForTab);

    // Reset offset when search term changes
    offsetRef.current = 0;
    promotedContentOffsetRef.current = 0;

    if (
      !isActiveTabCard &&
      !isEmptyResult &&
      searchTerm === searchStateForTab.searchQuery &&
      skillId === searchStateForTab?.selectedSkillId &&
      isEqual(advancedFilters, searchStateForTab?.advancedFilters)
    ) {
      const {
        data,
        cachedOffset,
        aggregations,
        selectedAggs,
        options,
        cachedPromotedContent,
        cachedPromotedOffset,
        cachedTotalPromotedCount
      } = searchStateForTab;
      offsetRef.current = cachedOffset;
      promotedContentOffsetRef.current = cachedPromotedOffset;
      // This ensures that when switching from the card tab to another tab and searching in another page,cached filters are not set.
      // However, if filters are present in the URL, they need to be applied.
      const finalAggs = { ...selectedAggs, ...extractDisplayNamesFromUrlFilters(cachedFilters) };

      setFilters(finalAggs);
      setSelectedBoxes(finalAggs);
      setSelectedOptions(options);
      setSearchData(data);
      setPromotedContent(cachedPromotedContent);
      setTotalPromotedContent(cachedTotalPromotedCount);
      setTotalSearchResults(data?.[`${activeTab}s`]?.length);
      setAggregationsSearch(aggregations);
      setSuggestedSearchTerm(data.suggestion);
      setIsSearchLoading(false);
    } else {
      setIsSearchLoading(true);
      setPromotedContent([]);
      setTotalSearchResults(0);
      resetSearchData(); // whenever search term, skillid or advanced filter changes we reset the prevSearch data
      const payload = getPayload();
      getSearchResults(payload);
      getPromotedContent(payload);
    }
  }, [activeTab, searchTerm, skillId, JSON.stringify(advancedFilters)]);

  useEffect(() => {
    getActiveLinkUrl(location?.pathname);
  });

  useEffect(() => {
    // Set filters in url
    if (isActiveTabCard) {
      setUrlFilters(transformFiltersForUrl());
    }

    if (!isEmpty(searchData)) {
      saveDataToReducer(searchData, aggregationsSearch, selectedAggregations, promotedContent);
    }
  }, [searchData, aggregationsSearch, selectedAggregations, promotedContent]);

  useEffect(() => {
    if (callapi) {
      filterResults(true, languagesReverse, contentSources, userNamesRef.current);
    }
  }, [callapi, filters]);

  useEffectAfterInitialMount(() => {
    if (
      isEmpty(searchStateForTab) ||
      searchTerm !== searchStateForTab?.searchQuery ||
      skillId !== searchStateForTab?.selectedSkillId ||
      !isEqual(advancedFilters, searchStateForTab?.advancedFilters) ||
      sortBy !== searchStateForTab?.sortBy
    ) {
      filterResults(true, languagesReverse, contentSources, userNamesRef.current);
    }
  }, [sortBy]);

  useEffectAfterInitialMount(() => {
    /*
     * Two-phase promoted content loading strategy:
     * 1. Initial page load: First API call fetches 14 promoted items
     * 2. After initial render: When offset = 14, second API call fetches next 14 items
     * 3. Final state: Total 28 promoted items fetched
     *
     * This effect triggers phase 2 once promotedContentOffsetRef reaches the API limit (14)
     */
    if (promotedContentOffsetRef.current === PROMOTED_CONTENT_API_LIMIT) {
      getPromotedContent(getPayload(), true);
    }
  }, [promotedContent]);

  const getPayload = () => {
    return {
      searchTerm,
      userLang: null,
      activeTab,
      source: 'search',
      isOrgAdmin,
      offset: offsetRef.current,
      optionalPayload: getOptionalPayload(selectedOptions, activeTab, standardTypeFilterKeyLabels),
      isfilterApplied: isSearchFilterApplied(selectedOptions),
      languagesReverse,
      sourceNames: contentSources,
      userNames: userNamesRef.current,
      sortBy
    };
  };

  const getSearchResults = async payload => {
    try {
      const response = await getSearchContent({
        ...payload,
        offsetRef,
        skillId,
        searchWithRecommendedSkills
      });

      if (response.suggestion && isActiveTabCard) {
        setSuggestedSearchTerm(response.suggestion);
      }

      const oldAggregations = search?.get(activeTab)?.aggregations;

      //Fetching aggregations from API or previous search based on location query change or if aggregations do not exist
      let aggregations = updateCounts(selectedAggregations, oldAggregations, response.aggs);

      if (response['total_items'] > 0) {
        const aggregationArguments = {
          data: aggregations,
          activeTab,
          proficiencyLevels,
          currentUserLang
        };

        aggregations = getAggregations(aggregationArguments);
      }

      setAggregationsSearch(aggregations);
      setTotalSearchResults(prevValue => prevValue + (response[`${activeTab}s`] || []).length);

      setIsSearchLoading(false);
      setIsLoadingMore(false);

      if (offsetRef.current == 0) {
        resetSearchData();
      }

      if (isLmsEnabledInstance && isActiveTabCard) {
        setSearchData(prevSearchData => {
          return {
            ...response,
            cards: [...(prevSearchData?.cards || []), ...response.cards]
          };
        });
      } else {
        setSearchData({
          ...(offsetRef.current === 0 ? {} : searchData),
          ...(offsetRef.current
            ? { [`${activeTab}s`]: [...searchData[`${activeTab}s`], ...response[`${activeTab}s`]] }
            : response)
        });
      }

      recordSearchEvent(response);
      setCallApi(false);
      if (enablesBatchLoadingOptimization) {
        // Increment displayed card count initially by by 12 (standard chunk size) when internal api is fetched and results returned
        setDisplayedCardCount(INFO_API_CARDS_BATCH_SIZE);
      }
    } catch (err) {
      console.error(`Error in SearchWrapper.getSearchResults: ${err}`);
    }
  };

  const getPromotedContent = async (payload, loadmore = false) => {
    // Since getPromotedContent() is called from multiple places hence added this config check here
    // Do not call the API when user searches with skill
    if (isPromotedContentSearchEnabled && !searchWithRecommendedSkills) {
      try {
        const promotedContentPayload = {
          ...payload,
          offset: promotedContentOffsetRef.current,
          offsetRef: promotedContentOffsetRef,
          isOfficial: true
        };

        const response = await getSearchContent(promotedContentPayload);

        if (response?.data) {
          let accessibleCards;
          if (loadmore) {
            accessibleCards = [...promotedContent, ...response?.cards]?.filter(getAccessibleCards);

            setPromotedContent(accessibleCards);
          } else {
            accessibleCards = response?.cards?.filter(getAccessibleCards);

            setPromotedContent(accessibleCards);
          }
          setTotalPromotedContent(accessibleCards?.length);
          promotedContentOffsetRef.current += PROMOTED_CONTENT_API_LIMIT;
        }
      } catch (err) {
        console.error(`Error in SearchWrapper.getSearchResults: ${err}`);
      }
    } else {
      resetPromotedResult();
    }
  };

  const onNextClick = () => {
    if (
      promotedContentOffsetRef.current <= totalPromotedContent &&
      promotedContentOffsetRef.current < NO_OF_PROMOTED_CONTENT_TO_SHOW
    ) {
      getPromotedContent(getPayload(), true);
    }
  };

  const resetSearchData = () => {
    setSearchData({});
    setDisplayedCardCount(0);
  };

  const resetPromotedResult = () => {
    promotedContentOffsetRef.current = 0;
    setPromotedContent([]);
    setTotalPromotedContent(0);
  };

  /**
   * Determines whether more data is available to load and if the "Show More" button should be displayed.
   *
   * This function handles two different scenarios to optimize the user experience and network usage:
   *
   * 1. Batch Loading Optimization (when LD.internalContentSearchLimit() > 60):
   *    - We fetch a large batch of data (e.g., 84 items) from the API but only process and display
   *      a smaller chunk (12 items) at a time to maintain UI responsiveness
   *    - This reduces API calls while still providing a smooth loading experience
   *
   * 2. Standard Loading (when batch optimization is disabled):
   *    - We fetch and display smaller batches directly from the API
   *    - This is used for skill searches or non-card tabs where batch optimization isn't beneficial
   *
   * The function ensures users can access all available results while balancing performance concerns.
   *
   * @returns {boolean} True if more data is available to load, false otherwise
   */
  const hasMoreData = () => {
    if (isActiveTabCard) {
      // Scenario 1: Batch Loading Optimization
      // Check if there are more unprocessed items in the current data batch
      // We do this check first to avoid unnecessary API calls when we already have data in memory
      if (enablesBatchLoadingOptimization && searchData?.data?.length > displayedCardCount) {
        return true;
      }
      // Scenario 2: Standard API-based Loading
      // Check if there are more items to fetch from the internal API
      return (
        searchData?.total_items > offsetRef.current + getSearchApiLimit(searchWithRecommendedSkills)
      );
    }

    return totalSearchResults < searchData?.total_items;
  };

  const saveDataToReducer = (data, aggregations, selections, promotedData) => {
    setSelectedAggregations(filters);
    dispatch(
      saveSearchTabState(activeTab, {
        data,
        sortBy,
        cachedOffset: offsetRef.current,
        aggregations: aggregations,
        selectedAggs: selections,
        searchQuery: searchTerm,
        advancedFilters,
        cachedPromotedContent: promotedData,
        cachedTotalPromotedCount: totalPromotedContent,
        cachedPromotedOffset: promotedContentOffsetRef.current,
        options: selectedOptions,
        ...(isActiveTabCard && {
          selectedSkillId: skillId,
          // Cache without filters
          cardsTabQueryParams: removeFilterQueryParam('filters')
        }),
        currentUserLanguageName
      })
    );
  };

  const recordSearchEvent = res => {
    const payload = {
      search_query: searchTerm,
      results_count: res?.total_items || res?.total || '0'
    };
    sendSearchAnalyticsEvent(payload);
  };

  const filterResults = (isfilterApplied, reverse, sourceNames, userNames) => {
    offsetRef.current = 0;
    userNamesRef.current = userNames;
    setShowFilterModal(false);
    setIsSearchLoading(true);
    setTotalSearchResults(0);
    resetSearchData();
    resetPromotedResult();

    const payload = getPayload();

    payload.isFilterApplied = isfilterApplied;
    payload.languagesReverse = reverse;
    payload.sourceNames = sourceNames;
    payload.userNames = userNamesRef.current;

    getSearchResults(payload);

    getPromotedContent(payload);
  };

  const toggleFilterModal = () => {
    setShowFilterModal(!showFilterModal);
  };

  /**
   * Handles the "Show More" button click to load additional content.
   * This function implements a multi-phase loading strategy:
   *
   * IMPORTANT DISTINCTION:
   * - searchData.data: Data returned from the internal content API (getInternalContentSearch)
   *                   This contains ALL cards from the API response, but only the first 12 are initially processed
   * - searchData.cards: Only contains cards that have been processed by getCardsInfoData
   *
   * Loading process:
   * 1. Initial load: The internal content API returns many cards (in searchData.data), but we only process
   *    and display the first 12 cards (in searchData.cards)
   * 2. On "Show More" click: We process the next 12 cards from searchData.data and add them to searchData.cards
   * 3. When all cards in searchData.data are processed: We make another call to the internal content API
   *    to fetch more data, and the cycle repeats
   */
  const showMoreClickHandler = async () => {
    // Go with normal flow if not content tab and if it's skill search
    if (!enablesBatchLoadingOptimization) return loadMoreFromExistingData();
    const { data } = searchData; // Data from internal content API

    // Check if we have more cards in the existing data that haven't been displayed yet
    // Checking data here instead of cards is because cards will have already been loaded in UI and data will have fresh cards received from internal content api
    if (data?.length > displayedCardCount) {
      setIsLoadingMore(true);
      try {
        // Calculate the range of cards to display next
        const nextBatchStart = displayedCardCount;
        const nextBatchEnd = Math.min(nextBatchStart + INFO_API_CARDS_BATCH_SIZE, data.length);
        // Extract the next batch of cards from data
        const nextBatch = data.slice(nextBatchStart, nextBatchEnd);

        // Call the info api
        const processedCards =
          (await getCardsInfoData(nextBatch, searchData.languagesMerged)) || [];

        // Add the newly processed cards to searchData.cards (what's displayed in the UI)
        setSearchData(prev => ({
          ...prev,
          cards: [...(prev.cards || []), ...processedCards]
        }));

        // Track how many cards from the internal content api data have been processed
        setDisplayedCardCount(prev => prev + INFO_API_CARDS_BATCH_SIZE);
      } catch (error) {
        // Handle error
        console.error('Error fetching more cards:', error);
      }
      setIsLoadingMore(false);
    } else {
      // If we've displayed all cards from internal content api data, fetch more from the same API
      // The 'true' parameter resets displayedCardCount to prepare for new data
      loadMoreFromExistingData(true);
    }
  };

  const loadMoreFromExistingData = (resetDisplayedCount = false) => {
    offsetRef.current = offsetRef.current + getSearchApiLimit(searchWithRecommendedSkills);
    const payload = getPayload();
    setIsLoadingMore(true);
    if (resetDisplayedCount) {
      setDisplayedCardCount(0);
    }
    getSearchResults(payload);
  };

  const searchSuggestedTerm = () => {
    // Appending current filters to new  URL if present
    if (currentUrlFilters) {
      const preservedParams = new URLSearchParams();
      preservedParams.set('filters', JSON.stringify(currentUrlFilters));
      navigate(
        `/smartsearch?q=${suggestedSearchTerm}&${preservedParams
          .toString()
          .replace(/&filters/, '%26filters')}`
      );
    } else {
      navigate(`/smartsearch?q=${suggestedSearchTerm}`);
    }
    setSuggestedSearchTerm('');
  };

  const resultsFromLabel = isActiveTabCard && skillLabel ? skillLabel : searchTerm;

  const searchCount =
    isLmsEnabledInstance && tabName === 'Content'
      ? !orgConfigs?.OrgCustomizationConfig?.web?.content['web/content/disableSearchCount']?.value
        ? searchData?.total_items || searchData?.total || '0'
        : ''
      : searchData?.total_items || searchData?.total || '0';

  const showSearchResultsInfoToolTipMessage = isActiveTabCard && (isLmsEnabledInstance || skillId);
  /**
    Returns the tooltip message for search results based on the current search settings.
  **/
  const getSearchResultsInfoToolTipMessage = () => {
    if (skillId && isLmsEnabledInstance) {
      return `${translatr('web.search.main', 'SearchResultsAccessInfoMessage')} <br/> ${translatr(
        'web.search.main',
        'AdvanceSearchFiltersNotAppllied'
      )}`;
    } else if (skillId) {
      return translatr('web.search.main', 'AdvanceSearchFiltersNotAppllied');
    } else if (isLmsEnabledInstance) {
      return translatr('web.search.main', 'SearchResultsAccessInfoMessage');
    }
  };

  const removeCardFromList = cardId => {
    const updatedCards = promotedContent.filter(card => card.id !== cardId);
    setPromotedContent(updatedCards);
  };

  const renderResults = () => {
    return (
      <>
        {promotedContent?.length > 0 && (
          <PromotedContent
            activeTab={activeTab}
            isSearchLoading={isSearchLoading}
            data={promotedContent}
            onNextClick={onNextClick}
            removeCardFromList={removeCardFromList}
            isNewSearchConfigEnable={isNewSearchConfigEnable}
          />
        )}
        <SearchedResults
          data={searchData}
          activeTab={activeTab}
          isSearchLoading={isSearchLoading}
          isLoadingMore={isLoadingMore}
          showMore={showMoreClickHandler}
          totalSearchResults={totalSearchResults}
          hasMoreData={hasMoreData}
          isNewSearchConfigEnable={isNewSearchConfigEnable}
        />
      </>
    );
  };

  const hasSearchResults =
    isLmsEnabledInstance && isActiveTabCard ? searchData?.total_items > 0 : totalSearchResults > 0;

  const filtersProps = {
    activeTab,
    tabName,
    filterResults,
    toggleFilterModal,
    showFilterModal,
    selectedOptions,
    setSelectedOptions,
    setFilters,
    filters,
    setCallApi,
    sortBy,
    setSortBy,
    contentSources,
    setShowPublicCards,
    selectedBoxes,
    setSelectedBoxes,
    aggregationsSearch,
    userNamesRef,
    additionalDetails,
    currentUserLanguageName,
    isNewSearchConfigEnable
  };

  const selectedFiltersProps = {
    selectedOptions,
    setSelectedOptions,
    filterResults,
    filters,
    setFilters,
    setCallApi,
    setShowPublicCards,
    selectedBoxes,
    setSelectedBoxes,
    activeTab,
    proficiencyLevels,
    currentUserLang,
    currentUserLanguageName,
    isNewSearchConfigEnable
  };

  const resultsInfoProps = {
    isActiveTabCard,
    searchCount,
    resultsFromLabel,
    skillLabel,
    showSearchResultsInfoToolTipMessage,
    getSearchResultsInfoToolTipMessage,
    suggestedSearchTerm,
    searchSuggestedTerm,
    shouldShowNewSearch,
    activeTab
  };

  const savePeopleFilters = (val, val2) => {
    setPeopleFilters({ ...val, navigation: val2 });
  };

  const renderNewSearchUIResults = () => (
    <div className="new-search-result-wrapper">
      {!isSearchLoading && (
        <div className={`justflex result-info ${activeTab}-result-info flex-column`}>
          <div className="flex-center flex-column filter-options">
            {hasSearchResults && (
              <Filters
                {...filtersProps}
                isFlyoutOpen={isFlyoutOpen}
                setFlyoutOpen={setFlyoutOpen}
              />
            )}
          </div>
          {Object.keys(selectedOptions)?.length > 0 &&
            // Only show if filters arrays contain values
            Object.keys(filters)?.some(key => filters[key]?.length > 0) && (
              <div className="flex-center selected-filters">
                <SelectedFilters {...selectedFiltersProps} setFlyoutOpen={setFlyoutOpen} />
              </div>
            )}
          <hr className="new-search-divider flex-center" />
          <div
            className={classNames('justflex flex-space-between result-sortby', {
              ' m-padding-bottom': !hasSearchResults
            })}
          >
            <ResultsInfo {...resultsInfoProps} />
            {isActiveTabCard && hasSearchResults && (
              <div className="flex-end flex-column">
                <div className="align-items-center justflex flex-end supporting-text sortby-heading">
                  {translatr('web.search.main', 'SortBy')}
                </div>
                <div className="sortby-input">
                  <Select
                    items={sortItems}
                    defaultValue={sortBy || sortItems[0].value}
                    onChange={item => setSortBy(item?.value?.trim())}
                    translateDropDownOptions={false}
                    ariaLabel={translatr('web.search.main', 'SortBy')}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {renderResults()}
    </div>
  );

  if (tabName === 'People') {
    return (
      <SearchPeopleGlobal
        globalSearchInfo={{
          searchValue: resultsFromLabel,
          total: searchCount,
          peopleFilters,
          savePeopleFilters
        }}
      />
    );
  }

  if (OpportunityTypes.includes(activeTab)) {
    return <SearchOpportunitiesGlobal searchValue={searchTerm} activeTab={activeTab} />;
  }

  return (
    <>
      {!shouldShowNewSearch ? (
        <>
          {!isSearchLoading && (
            <>
              <div className={`justflex result-info ${activeTab}-result-info`}>
                <ResultsInfo {...resultsInfoProps} />
                {hasSearchResults && <Filters {...filtersProps} />}
              </div>
              <SelectedFilters {...selectedFiltersProps} />
            </>
          )}
          {renderResults()}
        </>
      ) : (
        renderNewSearchUIResults()
      )}
    </>
  );
};

SearchWrapper.propTypes = {
  search: object,
  activeTab: string,
  searchTerm: string,
  advancedFilters: object,
  tabName: string,
  tabLabel: string,
  isOrgAdmin: bool,
  getActiveLinkUrl: func,
  languagesReverse: object,
  setShowPublicCards: func,
  showPublicCards: bool,
  isSearchSuggestionsEnabled: bool,
  taxonomyDomain: object,
  currentUserLang: string,
  isEgtEnabled: bool,
  isOcgEnabled: bool,
  showRecommendedSkills: bool,
  orgConfigs: object,
  proficiencyLevels: array,
  theme: string,
  isNewSearchConfigEnable: bool,
  peopleFilters: object,
  setPeopleFilters: func,
  isPromotedContentSearchEnabled: bool
};

SearchWrapper.defaultProps = {
  peopleFilters: {},
  setPeopleFilters: () => {}
};

const mapStoreStateToProps = ({ team, search, currentUser, theme }) => {
  const predefinedLanguages = team.get('languageDetails');
  const languagesReverse = {};
  predefinedLanguages.forEach(lang => {
    const { displayName, code } = lang;
    languagesReverse[code] = displayName;
  });
  languagesReverse['un'] = 'Unspecified';
  const teamConfig = team.get('config');
  const NoLevelproficiency = { name: 'No Level', value: NO_LEVEL };

  return {
    search: search,
    languagesReverse,
    isOrgAdmin: currentUser.get('isOrgAdmin'),
    taxonomyDomain: teamConfig?.taxonomy_domain,
    currentUserLang: currentUser?.get('profile')?.get('language') || 'en',
    isOcgEnabled: team.get('isOcgEnabled') || window.__ED__.isOcgEnabled,
    isEgtEnabled: team.get('isEgtEnabled'),
    isSearchSuggestionsEnabled: currentUser.get('isSearchSuggestionEnabled'),
    showRecommendedSkills:
      teamConfig?.show_recommended_skills && teamConfig?.ai_generated_skills > 0,
    orgConfigs: team.get('config'),
    proficiencyLevels: [NoLevelproficiency, ...team.get('proficiencyLevels')],
    theme: theme?.get('themeId'),
    isPromotedContentSearchEnabled: currentUser
      .get('globalSearchSettings')
      ?.get('isPromotedContentSearchEnabled')
  };
};

export default connect(mapStoreStateToProps)(SearchWrapper);
