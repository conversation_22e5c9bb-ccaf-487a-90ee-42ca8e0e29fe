import React from 'react';
import { Link } from 'centralized-design-system/src/Links';
interface CardOptionItemProps {
  label: string
  onClick?: () => void,
  to?: string
}

const CardOptionItem: React.FC<CardOptionItemProps> = ({ label, onClick, to }) => {
  const content = to ? (
    <Link
      color='secondary'
      to={to}
    >
      {label}
    </Link>
  ) : (
    <button className="pointer" tabIndex={0} onClick={onClick} type="button" role="menuitem">
      {label}
    </button>
  );

  return <li>{content}</li>;
};

export default CardOptionItem;
