import React, { useState } from 'react';
import Dropdown from 'centralized-design-system/src/Dropdown';
import { translatr } from 'centralized-design-system/src/Translatr';
import CardOptionItem from './card-option-item';
import { connect } from 'react-redux';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import classNames from 'classnames';
import FocusLock from 'react-focus-lock';

interface Option {
  label: string
  onClick?: () => void
  to?: string
}

interface CardOptionsProps {
  options: Array<Option>
  title: string
  theme: string
  moreActionButtonRef?: React.RefObject<HTMLDivElement>
}

const CardOptions: React.FC<CardOptionsProps> = ({ options, title, theme, moreActionButtonRef }) => {

  const [openMenu, setOpenMenu] = useState(false);
  const isNewDesignEnabled = theme == ThemeId.PLARE;

  return (
    <div className="card-options">
      <Dropdown
        icon={<i className="icon-ellipsis-h icon card-icon" />}
        onClickCB={e => {
          // Prevent event propagation to fix blinking
          e.stopPropagation();
        }}
        onClickPreLogic={() => {
          // Prevent multiple open dropdowns
          document.getElementById('edc-web-body').click();
        }}
        setOpenDropdown={setOpenMenu}
        clickListenerCB={() => setOpenMenu(false)}
        openDropdown={openMenu}
        ariaLabel={translatr('web.common.main', 'MoreOpportunityActions', { opportunity: title })}
        ariaHasPopup="menu"
        closeOnTab={true}
        ref={moreActionButtonRef}
      >
        <FocusLock returnFocus autoFocus>
          <ul
            className={classNames('sourcing-dropdown-list', { 'plate-theme': isNewDesignEnabled })}
            role="menu"
          >
            {options.map(({ label, onClick, to }) => (
              <CardOptionItem
                key={label}
                label={label}
                to={to}
                onClick={() => {
                  onClick?.();
                  setOpenMenu(false);
                }}
              />
            ))}
          </ul>
        </FocusLock>
      </Dropdown>
    </div>
  );
}

export default connect((state: any) => ({ theme: state.theme?.get('themeId') }))(CardOptions);
