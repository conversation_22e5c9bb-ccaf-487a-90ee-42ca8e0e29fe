import React from 'react';
import { useLocation } from 'react-router-dom';
import { translatr } from 'centralized-design-system/src/Translatr';
import InfoSection from './info-section';
import TitleWithCounter from '../../common/title-with-counter/title-with-counter';
import CardOptions from '../../common/card-options/card-options';
import { Location } from '../../../../types/manage-page/api/common.types';
import "../../styles.scss";
import { Organization } from '@pages/Sourcing/types/manage-page/api/api.types';
import VerticalDivider from '../../common/vertical-divider/vertical-divider';
import { connect } from 'react-redux';
import { ThemeId } from 'centralized-design-system/src/Theme/ThemeInterface';
import classNames from 'classnames';
import LayoutDivider from '@pages/TalentMarketplace/shared/CandidateListing/LayoutDivider/LayoutDivider';

interface MainPageProps {
  id: string
  title: string
  referenceNumber: string
  locations: Array<Location>
  remote: string
  endDate: string
  startDate: string
  bookmarkedCount: number
  appliedCount: number
  isBookmarked: boolean
  handleBookmark: (id: string, source: string, isBookmarked: boolean) => void,
  jobFunctionName: string,
  jobFamilyName: string,
  organizations: Organization[]
  shortlistedCount: number
  theme: string
}

const MainPage: React.FC<MainPageProps> = ({ id, title, referenceNumber, locations, remote, endDate, startDate, bookmarkedCount, appliedCount, isBookmarked, handleBookmark,
  jobFunctionName, jobFamilyName, organizations, shortlistedCount, theme
 }) => {
  const location = useLocation();

  const path = location.pathname;

  const options = [{
    label: translatr('web.sourcing.main', 'ViewDetails'),
    to: `/career/detail/job_vacancy/${encodeURIComponent(id)}`
  }, {
    label: translatr('web.sourcing.main', 'ManageJobVacancy'),
    to: `${path}/manage/job_vacancy/${encodeURIComponent(id)}`
  }, {
    label: translatr('web.common.main',`${isBookmarked ? 'Unbookmark' : 'Bookmark'}`),
    onClick: () => handleBookmark(id, 'job_vacancy', isBookmarked)
  }];

  const isNewDesignEnabled = theme == ThemeId.PLARE;
  return (
    <div className={classNames('desc-card ts-desc-card-container', { 'edcast-ui': !isNewDesignEnabled })}>
     <InfoSection
        id={id}
        title={title}
        referenceNumber={referenceNumber}
        locations={locations}
        remote={remote}
        endDate={endDate}
        startDate={startDate}
        jobFunctionName={jobFunctionName}
        jobFamilyName={jobFamilyName}
        organizations={organizations}
     />
     <div className="desc-col-2">
       <TitleWithCounter
         title={translatr('web.sourcing.main', 'Bookmarks')}
         tooltipMessage={translatr('web.sourcing.candidate-profile', 'BookmarkTooltipDescription')}
         count={bookmarkedCount}
       />
       <div className={classNames("dash-line-container", {
        'ed-ui': !isNewDesignEnabled,
        'plare': isNewDesignEnabled
       })}>
        {isNewDesignEnabled
          ? <VerticalDivider /> :
          <LayoutDivider align="vertical" />
        }
       </div>
       <TitleWithCounter
         title={translatr('web.sourcing.main', 'Applied')}
         tooltipMessage={translatr('web.sourcing.candidate-profile', 'AppliedTooltipDescription')}
         count={appliedCount}
       />
       <div className={classNames("dash-line-container", {
        'ed-ui': !isNewDesignEnabled,
        'plare': isNewDesignEnabled
       })}>
        {isNewDesignEnabled
          ? <VerticalDivider /> :
          <LayoutDivider align="vertical" />
        }
       </div>
       <TitleWithCounter
         title={translatr('web.sourcing.candidate-profile', 'Shortlisted')}
         tooltipMessage={translatr('web.sourcing.candidate-profile', 'NumberOfShortlistedTalent')}
         count={shortlistedCount}
       />
       <CardOptions options={options} title={title} />
     </div>
   </div>
 );
};

export default connect((state: any) => ({ theme: state.theme?.get('themeId') }))(MainPage);
