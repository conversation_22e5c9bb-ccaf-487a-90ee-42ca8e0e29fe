/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import DatePicker from 'centralized-design-system/src/DatePickers';
import Loading from 'centralized-design-system/src/Loading';
import Table from 'centralized-design-system/src/Table';
import { Select, SearchInput } from 'centralized-design-system/src/Inputs';

import { getUserPerformance } from '../../../../app/api/performance';
import { renderMessage } from '../../manager_dashboard/common';
import { UserPerformanceTranslations } from '../translations';
import {
  getTdClass,
  getDefault,
  getLabel,
  isClickable,
  getDrilldown,
  linkTo,
  updateData,
  decodeHtml
} from '../tableUtil';
import {
  downloadCSV,
  getTeamOptions,
  modalSortData,
  sortData,
  shouldCloseOnSelect
} from '../utils';
import moment from 'moment';
import { TIMESTAMP_WITH_SECONDS_FORMAT } from '@utils/constants';
import unescape from 'lodash/unescape';

const report_label = translatr('web.analytics-reports.main', 'ReportDescLabel');

export const UserPerformance = props => {
  const teamOptions = getTeamOptions(props.teams);
  const [error, setError] = useState('');
  const [isLoading, setLoading] = useState(true);
  const [lastUpdated, setLastupdated] = useState('');
  const [limit] = useState(1000);
  const [modalData, setModalData] = useState([]);
  const [modalHeaders, setmodalHeaders] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalSortDir, setModalSortDir] = useState('desc');
  const [modalTitle, setModalTitle] = useState('');
  const [searchStr, setSearchStr] = useState('');
  const [sortKey, setSortKey] = useState('card_viewed');
  const [sortDir, setSortDir] = useState('desc');
  const [teamSelected, setTeamSelected] = useState(teamOptions[0]);
  const [total, setTotal] = useState(0);
  const [totalModalData, setTotalModalData] = useState(0);
  const [userData, setUserData] = useState([]);
  const [headers] = useState([
    'user_name',
    'card_created',
    'card_marked_as_complete',
    'card_viewed',
    'card_liked',
    'card_shared',
    'card_comment_created',
    'card_bookmarked',
    'followers_count',
    'followed_count',
    'user_last_sign_in_at'
  ]);

  useEffect(() => {
    getUserData();
  }, [props.newDates, teamSelected]);

  async function getUserData(str = '') {
    setLoading(true);
    setTotal(0);
    let payload = {
      page: 1,
      limit,
      sortBy: [`${sortKey} ${sortDir}`],
      fromDate: props.newDates.fromDate,
      toDate: props.newDates.toDate,
      teams: props.teams && (teamSelected.id === 0 ? props.teams.map(t => t.id) : [teamSelected.id])
    };
    if (str?.trim()?.length > 0) {
      payload['search'] = { user_name: str?.trim() };
    }

    const res = await getUserPerformance(payload)
      .catch(err => {
        console.error(err);
        setError(`Error in User Performance: ${err}`);
      })
      .finally(() => {
        setLoading(false);
      });
    initUserData(res);
  }

  function initUserData(res) {
    if (res?.facts_data) {
      let toDt = moment.utc(res.last_updated_at, TIMESTAMP_WITH_SECONDS_FORMAT).toDate();
      const lastUpdatedAt = moment(toDt).format(TIMESTAMP_WITH_SECONDS_FORMAT);

      setUserData(res.facts_data);
      setTotal(res.total);
      setError('');
      setLastupdated(lastUpdatedAt || '');
    } else {
      setUserData([]);
      setTotal(0);
      setLastupdated('');
    }
  }

  function getSortedData(header) {
    const sd = sortDir === 'asc' ? 'desc' : 'asc';
    setSortKey(header.id);
    setSortDir(sd);
    setUserData(sortData({ sortKey: header.id, sortDir: sd, data: userData }));
  }

  function getModalSortedData(header) {
    const sd = modalSortDir === 'asc' ? 'desc' : 'asc';
    setModalSortDir(sd);
    setModalData(modalSortData({ sortKey: header.id, sortDir: sd, data: modalData }));
  }

  function handleDownload() {
    let localTimeData = userData.map(elem => {
      let toDt = moment.utc(elem.user_last_sign_in_at, TIMESTAMP_WITH_SECONDS_FORMAT).toDate();
      let d = moment(toDt).format(TIMESTAMP_WITH_SECONDS_FORMAT);
      elem.user_last_sign_in_at = d;
      return elem;
    });
    downloadCSV({ data: localTimeData, headers });
  }

  function getSearchData(str) {
    if (str === searchStr) {
      return;
    }
    setSearchStr(str);
    getUserData(str);
  }

  function onTeamChange(selectedOption) {
    if (!selectedOption || selectedOption.id === teamSelected.id) {
      return;
    }
    setTeamSelected(selectedOption);
  }

  async function handleDrilldown(e, event, label) {
    // Admin -> Settings -> Team Config
    if (!props.allowSubadminDrilldown) {
      return;
    }

    setModalTitle(label);
    setmodalHeaders([]);
    setModalData([]);
    setTotalModalData(0);
    setError('');

    const link = linkTo(event);
    if (link !== '') {
      let [path, attr] = link.split(':');
      if (!path || !attr) {
        console.error('Invalid link');
        setError(`Invalid link of drilldown: ${label}`);
        return;
      }

      try {
        let attrId = e.target.getAttribute(attr);
        window.open(`${path}${attrId}`, '_blank', 'noopener,noreferrer');
      } catch (err) {
        console.error(err);
        setError(`Error in drilldown: ${err}`);
      }
      return;
    }

    let opts = {
      table: 'user_card_performance_reporting_i',
      fromDate: props.newDates.fromDate,
      toDate: props.newDates.toDate,
      event,
      limit
    };

    try {
      let attrEl = e.target;
      if (attrEl.hasAttribute('data-user-id')) {
        opts.userId = parseInt(attrEl.getAttribute('data-user-id'));
      }
      if (attrEl.hasAttribute('data-user-identifier')) {
        opts.user_identifier = attrEl.getAttribute('data-user-identifier');
      }
    } catch (err) {}

    setModalOpen(true);
    setLoading(true);

    // Make our request for the info
    let hadError = false;
    let resp = await getUserPerformance(opts).catch(err => {
      console.error('Performance handleClick.func', err);
      hadError = true;
    });

    // We need to run this twice if the modal fails to respond
    if (hadError) {
      resp = await getUserPerformance(opts).catch(err => {
        console.error('Performance handleClick.func2', err);
        setError(`Error in User Performance: ${err}`);
      });
    }

    let data = [];
    let drilldownHeaders = [];
    let drilldownTotal = 0;
    let lastUpdatedAt;

    if (resp && resp.facts_data) {
      drilldownHeaders = Object.keys(resp.facts_data?.[0] || []);
      data = resp.facts_data;
      let drilldown = getDrilldown(event);
      if (drilldown) {
        drilldownHeaders = drilldown.filter(item => drilldownHeaders.includes(item));
      }
      data = updateData(drilldownHeaders, data);
      drilldownTotal = resp.total;

      let toDt = moment.utc(resp.last_updated_at, TIMESTAMP_WITH_SECONDS_FORMAT).toDate();
      lastUpdatedAt = moment(toDt).format(TIMESTAMP_WITH_SECONDS_FORMAT);
    }

    setmodalHeaders(drilldownHeaders);
    setModalData(data);
    setTotalModalData(drilldownTotal);
    setLastupdated(lastUpdatedAt || '');
    setLoading(false);
  }

  function renderMiddleActionBar() {
    return (
      <div className="middle-actions-bar">
        {modalOpen && (
          <button
            className="modal-back-label pointer s-margin-bottom"
            onClick={() => setModalOpen(false)}
          >
            <span className="icon-angle-left-arrow back-icon leftArrowRTL"></span>
            {modalTitle}
          </button>
        )}
        {!modalOpen && (
          <>
            {teamOptions.length > 0 && (
              <div className="team-selector">
                <Select items={teamOptions} onChange={onTeamChange} />
              </div>
            )}
            <div className="justflex middle-action-sec width-100">
              <div className="justflex calender-download-sec">
                <div className="calendar-container">
                  <DatePicker
                    shouldCloseOnSelect={shouldCloseOnSelect()}
                    singleDatePicker={false}
                    startDate={props.newDates.fromDate ? new Date(props.newDates.fromDate) : null}
                    endDate={props.newDates.toDate ? new Date(props.newDates.toDate) : null}
                    opens="right"
                    onChange={props.onDatechange}
                  />
                </div>
                <button
                  className="ed-btn ed-btn-neutral container-icon-download"
                  disabled={!total}
                  onClick={() => handleDownload()}
                >
                  <i className="icon-download"></i>
                  {translatr('web.analytics-reports.main', 'DownloadReport')}{' '}
                  {`(${total > 1000 ? '1000' : total})`}
                </button>
              </div>
              <div className="right-content search-container">
                <SearchInput
                  placeholder={translatr('web.common.main', 'SearchByUserName')}
                  value={searchStr}
                  onSearch={getSearchData}
                  maxLen={50}
                />
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  function renderTbData(val, key, item) {
    if (props.allowSubadminDrilldown && val != 0 && isClickable(key)) {
      const onClickHandleDrillDown = e => {
        handleDrilldown(
          e,
          key,
          `${getLabel(item['user_name'])} - ${translatr(
            UserPerformanceTranslations[key][0],
            UserPerformanceTranslations[key][1]
          )}`
        );
      };
      return (
        <>
          {key === 'user_name' && (
            // eslint-disable-next-line jsx-a11y/no-static-element-interactions
            <span
              role="button"
              className="drilldown"
              data-user-id={item.user_id}
              data-user-handle={item.user_handle}
              data-user-identifier={item.identifier}
              onClick={onClickHandleDrillDown}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  onClickHandleDrillDown();
                }
              }}
              tabIndex="0"
            >
              {val}
              <label className="email">{item.user_email}</label>
            </span>
          )}
          {key !== 'user_name' && (
            <span
              data-user-id={item.user_id}
              data-user-handle={item.user_handle}
              data-user-identifier={item.identifier}
              className={`drilldown ${typeof val === 'number' ? 'larger-font' : ''}`}
              onClick={onClickHandleDrillDown}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  onClickHandleDrillDown();
                }
              }}
              tabIndex="0"
              role="button"
            >
              {val}
            </span>
          )}
        </>
      );
    } else {
      return <span className="clamp">{decodeHtml(val)}</span>;
    }
  }

  function renderTableRecords() {
    // Create a set for what we want to show in the table
    const data = userData.slice(0, 2000);
    const TableHeaders = headers.map((header, i) => {
      const [route, key] = UserPerformanceTranslations[header];
      return {
        className: '',
        label: translatr(route, key),
        id: header,
        align: i === 0 || i === headers.length - 1 ? 'text-left' : 'text-right',
        sortable: true,
        onClick: getSortedData
      };
    });

    const TableData = data.map((item, i) => {
      const rows = [];
      headers.forEach((key, j) => {
        let val =
          (Number.isInteger(item[key]) && item[key]) || item[key]?.trim?.() || getDefault(key);
        if (key === 'user_last_sign_in_at') {
          try {
            if (item[key].trim()) {
              let toDt = moment.utc(item[key], TIMESTAMP_WITH_SECONDS_FORMAT).toDate();
              val = moment(toDt).format(
                `DD MMM, YYYY [${translatr('web.analytics-reports.main', 'At')}] h:mm:ss A z`
              );
            }
          } catch (e) {}
        }
        rows.push({
          label: '',
          id: `${item.user_id}-${i}-${j}`,
          children: renderTbData(val, key, item),
          align: getTdClass(key),
          disabled: !(props.allowSubadminDrilldown && val !== 0 && isClickable(key))
        });
      });
      return rows;
    });

    return (
      <>
        <div className="block mt-16 mb-16 p-0">
          <Table headers={TableHeaders} rows={TableData} />
        </div>
      </>
    );
  }

  function renderModalRecords() {
    const TableHeaders = modalHeaders.map(header => {
      const [route, key] = UserPerformanceTranslations[header];
      return {
        className: '',
        label: translatr(route, key),
        id: header,
        align: 'text-left',
        sortable: true,
        onClick: getModalSortedData
      };
    });

    const TableData = modalData.map((row, i) => {
      let key = row.id || row.user_id || row.card_id || row.group_id;
      key += `-${i}`;
      const rows = [];

      modalHeaders.forEach((h, j) => {
        let output = row[h];
        if (h === 'time' || h === 'user_last_sign_in_at') {
          try {
            if (row[h].trim()) {
              let toDt = moment.utc(row[h], TIMESTAMP_WITH_SECONDS_FORMAT).toDate();
              output = moment(toDt).format(
                `DD MMM, YYYY [${translatr('web.analytics-reports.main', 'At')}] h:mm:ss A z`
              );
            }
          } catch (e) {}
        }
        rows.push({
          label: output,
          id: `${key}-${j}`,
          align: getTdClass(h),
          children: <span className="drilldown">{unescape(output)}</span>
        });
      });
      return rows;
    });

    return (
      <>
        <div className="block mt-16 mb-16 p-0">
          <Table headers={TableHeaders} rows={TableData} />
        </div>
      </>
    );
  }

  function renderTable() {
    if (!modalOpen) {
      return <>{userData.length === 0 ? renderMessage(error) : renderTableRecords()}</>;
    } else {
      return <>{modalData.length === 0 ? renderMessage(error) : renderModalRecords()}</>;
    }
  }

  function renderTableFooter() {
    let label;
    if (modalOpen) {
      label = translatr('web.analytics-reports.main', 'ShowingTotalmodaldataOfTotalmodaldata', {
        totalModalData
      });
    } else {
      label =
        total > 1000
          ? translatr('web.analytics-reports.main', 'Showing1000OfTotal', { total })
          : translatr('web.analytics-reports.main', 'ShowingTotalOfTotal', { total });
    }

    return (
      <div className="reports-table-footer">
        <label>{label}</label>
        {lastUpdated && (
          <label>
            {translatr('web.analytics-reports.main', 'LastDataRefreshTimeLastupdated', {
              lastUpdated
            })}
          </label>
        )}
      </div>
    );
  }

  return (
    <div className="block reports users">
      {!modalOpen && <p className="middle-label">{report_label}</p>}
      {renderMiddleActionBar()}
      {isLoading ? <Loading /> : renderTable()}
      {!isLoading && renderTableFooter()}
    </div>
  );
};

UserPerformance.propTypes = {
  newDates: PropTypes.object,
  teams: PropTypes.array,
  allowSubadminDrilldown: PropTypes.bool,
  onDatechange: PropTypes.func
};
