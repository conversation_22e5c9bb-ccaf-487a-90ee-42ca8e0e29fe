import React, { useMemo, forwardRef, useId } from 'react';
import { useNavigate } from 'react-router-dom';
import TextClamp from '@components/TextClamp';
import { translatr, omp } from 'centralized-design-system/src/Translatr';
import { MATCH_LEVEL } from 'edc-web-sdk/requests/talentMarketplaceSettings';
import { MATCHING_ICONS, ICON_SET } from 'edc-web-sdk/helpers/matchIcons';
import { Tooltip as TmTooltip } from '@components/TextClamp/TextClamp';
import CareerPathGalaxy from '../../../../../app/icons/CareerPathGalaxy';
import { subway } from '../../CareerPathing/SubwayView/subway';
import cn from 'classnames';
import './RolePillSvg.scss';
import { ButtonLink } from 'centralized-design-system/src/Buttons';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';

interface RolePillSvgProps {
  roleId?: string;
  x?: number;
  y?: number;
  progress?: number;
  progressStatus?: string;
  name?: string;
  row?: number;
  col?: number;
  animate?: CareerPathingAnimConfig;
  isTarget?: boolean;
  hasMoreNextRoles?: boolean;
  isExpanded?: boolean;
  handleOver?: (event: React.MouseEvent) => void;
  handleLeave?: (event: React.MouseEvent) => void;
  handleClick?: (event: React.MouseEvent) => void;
  handleKeyDown?: (event: React.KeyboardEvent) => void;
  width?: number;
  height?: number;
  mode: "galaxy" | "subway";
  isSelected?: boolean;
  isLastElement?: boolean
  iconSet: string;
  jobFamilyName?: string,
  noOfMoves?: number,
  hideMatchingDetails?: boolean,
  onSelectedPath?: boolean
}

const RolePillSvg: React.FC<RolePillSvgProps> = forwardRef(({
  name,
  roleId,
  x = 0,
  y = 0,
  progress = 0,
  progressStatus,
  row = 0,
  col = 0,
  animate = { anim: true, animMode: 'anim-mode--1' },
  isTarget = false,
  isExpanded = true,
  hasMoreNextRoles = false,
  handleOver,
  handleLeave,
  handleClick,
  handleKeyDown,
  mode = 'galaxy',
  width = subway.getPillSize().width,
  height = subway.getPillSize().height,
  isSelected = false,
  isLastElement = false,
  iconSet,
  jobFamilyName,
  noOfMoves,
  hideMatchingDetails,
  onSelectedPath = false
}, ref) => {
  const tooltipId = useId();
  const navigate = useNavigate();
  const collapsedOutline = true;
  const collapsedSize = collapsedOutline ? 36 : 24;
  const rectProps = isExpanded
    ? {
      x: x - width / 2,
      y: y - height / 2
    }
    : {
      x: x - collapsedSize / 2,
      y: y - collapsedSize / 2
    };

  const { isAspirationalRole } = React.useContext(AspirationsContext);
  const isAspirational = isAspirationalRole(roleId);

  const showMatchIcon = !hideMatchingDetails && MATCH_LEVEL[progressStatus] ? true : false;

  const svgIcon = useMemo(() => {
    return `<svg width="25" height="24" fill="none" xmlns="http://www.w3.org/2000/svg" className="progress-smile-icon">${MATCHING_ICONS.get(iconSet || ICON_SET.FACES).get(MATCH_LEVEL[progressStatus])}</svg>`;
  }, [iconSet, progressStatus]);
  const svgIconCollapsed = useMemo(() => {
    return `
    <svg width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" className="progress-smile-icon">
      <g>
        <circle cx="18" cy="18" r="18" fill="white" />
        <g transform="translate(6,6)">${MATCHING_ICONS.get(iconSet || ICON_SET.FACES).get(MATCH_LEVEL[progressStatus])}</g>
      </g>
    </svg>`;
  }, [iconSet, progressStatus]);

  const getMoveLineLabel = (moveNr: number) => {
    return moveNr === 1
      ? translatr('web.talentmarketplace.career-path', 'Move', { cnt: moveNr })
      : translatr('web.talentmarketplace.career-path', 'Moves', { cnt: moveNr });
  };

  const ariaLabel = (typeof jobFamilyName !== 'undefined' && typeof getMoveLineLabel(noOfMoves) !== 'undefined')
    ? `${name} - ${omp('tm_job_family')}: ${jobFamilyName} - ${getMoveLineLabel(noOfMoves)}`
    : name;

  return (
    <g
      ref={ref}
      tabIndex={onSelectedPath ? 0 : -1}
      key={`pill-${name?.toLowerCase()}`}
      className={`g-button cp_role-pill--clickable ${isLastElement ? 'last-element' : ''}`}
      role="listitem"
      aria-label={ariaLabel}
      onMouseEnter={handleOver}
      onMouseLeave={handleLeave}
      onMouseDown={handleClick}
      onKeyDown={handleKeyDown}
      aria-expanded="false"
      aria-hidden="true"
      {...(isAspirational && { ["aria-description"]: omp('tm_tm_aspirational_role') })}
    >
      {isExpanded && (
        <>
          {isSelected && mode === 'subway' && (
            <rect
              x={rectProps.x - 5}
              y={rectProps.y - 5}
              width={width + 10}
              height={height + 10}
              rx="50"
              fill="white"
              className={cn(`cp-role-pill__bg row_${row} col_${col} ${animate.animMode}`, {
                'cp-role-pill__animation': animate.anim
              })}
            />
          )}
          <foreignObject
            x={rectProps.x}
            y={rectProps.y}
            width={width}
            height={height}
            className="cp_role-pill__object"
          >
            <div
              className={cn(`cp-role-pill__wrapper row_${row} col_${col} ${animate.animMode}`, {
                'cp-role-pill__animation': animate.anim,
                'cp-role-pill--highlighted': isTarget,
                'cp-role-pill--selected': isSelected,
                'cp-role-pill--aspirational': isAspirational,
                '--nointeraction': typeof handleClick === 'undefined'
              })}
              style={{
                width: `${width}px`,
                height: `${height}px`
              }}
            >
              {isAspirational && (
                <div
                  className="cp-role-pill__aspirational-info"
                  style={{
                    width: `${width}px`
                  }}
                  id={tooltipId}
                  role="tooltip"
                >
                  {omp('tm_tm_aspirational_role')}
                  <div className="aspirational-arrow"></div>
                </div>
              )}
              <div className={`cp-role-pill__content --status-${progressStatus?.toLowerCase()}`}>
                <div className="cp-role-pill__icon">
                  {showMatchIcon && (<img src={`data:image/svg+xml;utf8,${encodeURIComponent(svgIcon)}`} alt="" />)}
                </div>
                <div className="cp-role-pill__name" role='button'>
                  <TextClamp
                    tooltipPlacement={isAspirational ? 'bottom' : 'top'}
                    disableTooltip={name && name.length < 30}
                    line={2}
                  >
                    {name}
                  </TextClamp>
                </div>
              </div>
            </div>
            {hasMoreNextRoles && roleId && isTarget && (
              <div className="cp-role-pill-more-roles__button">
                <ButtonLink
                  color='secondary'
                  variant='ghost'
                  to={`/career/career-path/${encodeURIComponent(roleId)}`}
                  size='medium'
                >
                  <span>
                    <CareerPathGalaxy style={{ width: 17, height: 17 }} />
                  </span>
                  <span>
                    {translatr('web.talentmarketplace.career-path', 'NextRoles', {
                      roles: omp('tm_tm_job_roles')
                    })}
                  </span>
                </ButtonLink>
              </div>
            )}
          </foreignObject>
        </>
      )}
      {!isExpanded && (
        <>
          <title>{name}</title>
          <foreignObject
            x={rectProps.x}
            y={rectProps.y}
            width={collapsedSize}
            height={collapsedSize}
            className="cp_role-pill__object"
          >
            <TmTooltip title={name} placement="top" arrow>
              <div
                className={cn(`cp-role-pill__wrapper cp-role-pill--collapsed ${animate.animMode}`, {
                  'cp-role-pill__animation': animate.anim,
                  'cp-role-pill--selected': isSelected
                })}
              >
                <img src={`data:image/svg+xml;utf8,${encodeURIComponent(svgIconCollapsed)}`} alt="" />
              </div>
            </TmTooltip>
          </foreignObject>
        </>
      )}
    </g>
  );
});

export default RolePillSvg;
