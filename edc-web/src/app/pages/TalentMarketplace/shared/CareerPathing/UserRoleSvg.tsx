import React, {forwardRef, useState} from 'react';
import TextClamp from '@components/TextClamp';
import Avatar from 'centralized-design-system/src/Avatar';
import UserRoleSwitch from './UserRoleSwitch';
import './UserRoleSvg.scss';
import { preventReadLiveRegions } from '@pages/TalentMarketplace/util';
import { translatr } from 'centralized-design-system/src/Translatr';

interface UserRoleSvgProps {
  usrImg?: string;
  name?: string;
  roleId?: string;
  width?: number;
  height?: number;
  pillWidth?: number;
  pillHeight?: number;
  x?: number;
  y?: number;
  roleSwitch?: boolean;
  switchBackRoles?: UserRole[];
  roleSwitchTarget?: string;
  detailPanelRef?: React.RefObject<{
    closeDropdown: () => void;
  }>;
  handleKeyDown?: (event: React.KeyboardEvent) => void;
}

const UserRoleSvg: React.FC<UserRoleSvgProps> = forwardRef(({
  usrImg,
  name,
  roleId,
  width,
  height,
  pillWidth,
  pillHeight,
  x = 0,
  y = 0,
  roleSwitch = false,
  roleSwitchTarget,
  switchBackRoles = [],
  detailPanelRef,
  handleKeyDown
}, ref) => {
  const rectProps = {
    x: x - width / 2,
    y: y - height / 2 - 10,
    width: width,
    height: height
  };
  const [switchAnchorEl, setSwitchAnchorEl] = useState(null); //popover with detail of Role
  const isGalaxy = roleSwitch && roleSwitchTarget === 'galaxy';

  /* Sets the anchor for the role switch popover.
   * @param {event} event - The click event.
   */
  const dropdownHandleClick = (event: React.MouseEvent<HTMLElement>) => {
    setSwitchAnchorEl(event.currentTarget);
  };
  const dropdownHandleClose = () => {
    preventReadLiveRegions(); //prevent reading live regions when the popover are closed
    setSwitchAnchorEl(null);
  };
  return (
    <g
      key={`user-role-${name?.toLowerCase()}`}
      className="g-button"
    >
      <foreignObject
        x={rectProps.x}
        y={rectProps.y}
        width={Number(width) + 10}
        height={Number(height) + 10}
      >
        <div
          className="cp-user-role__wrapper"
          style={{
            width: `${width}px`
          }}
        >
          <div className="cp-user-role__avatar">
            <Avatar blankAlt="true" user={{ imgUrl: usrImg }} />
          </div>
          {roleSwitch
            ? (<>
            <button
              ref={ref}
              className="cp-user-role__name"
              style={{
                height: `${pillHeight}px`,
                width: `${pillWidth}px`
              }}
              onClick={e => dropdownHandleClick(e)}
              onKeyDown={handleKeyDown}
              aria-expanded={Boolean(switchAnchorEl)}
              aria-describedby={isGalaxy ? "galaxy-view-keyboard-navigation" : undefined}
            >
              <TextClamp className="cp-user-role__name--clamp" line={2}>
                {name}
              </TextClamp>
              <div className="cp-user-role__search-dropdown">
                  <span className="icon-angle-down-arrow"></span>
              </div>
            </button>
            {isGalaxy && (<div id="galaxy-view-keyboard-navigation" className="sr-only" aria-hidden="true">{translatr('web.talentmarketplace.main', 'CareerPathNavigationDescription',)}</div>)}
            </>)
            : (<div
              ref={ref}
              className="cp-user-role__name"
              style={{
                height: `${pillHeight}px`,
                width: `${pillWidth}px`
              }}
              tabIndex={-1}
            >
              <TextClamp className="cp-user-role__name--clamp" line={2}>
                {name}
              </TextClamp>
            </div>)
          }

          {switchAnchorEl && (
            <UserRoleSwitch
              anchorEl={switchAnchorEl}
              onClose={dropdownHandleClose}
              roleId={roleId}
              switchBackRoles={switchBackRoles}
              detailPanelRef={detailPanelRef}
              roleSwitchTarget={roleSwitchTarget}
            />
          )}
        </div>
      </foreignObject>
    </g>
  );
});

export default UserRoleSvg;
