import React from 'react';
import { ButtonLink } from 'centralized-design-system/src/Buttons';
import Loading from 'centralized-design-system/src/Loading';
import Checkbox from 'centralized-design-system/src/Checkbox';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import HorizontalMentorshipCard from '@components/MentorCard/HorizontalMentorshipCard';
import { DevPlanMentor, DevPlanStep } from '@pages/TalentMarketplace/DevelopmentPlan/constants';
import { OPPORTUNITY_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';

interface MentorSearchResultsProps {
  loading: boolean;
  mentorList: DevPlanMentor[];
  selectedMentors: string[];
  toggleMentorSelection: (id: string) => void;
  saving: boolean;
  loadingError: boolean;
  currentStepData: DevPlanStep;
  searchTerm: string;
  headerTitle: string;
}

const MentorSearchResults: React.FC<MentorSearchResultsProps> = ({
  loading,
  mentorList,
  selectedMentors,
  toggleMentorSelection,
  saving,
  loadingError,
  currentStepData,
  searchTerm,
  headerTitle
}) => {
  const actualMentorsInPlan =
    currentStepData?.mentors
      ?.filter(({ status }) => status !== 'REMOVED')
      ?.map(mentor => mentor.id) || [];

  return (
    <>
      {headerTitle && <h3 className="devplan-additem-modal__results-header">{headerTitle}</h3>}
      <ul className={`devplan-additem-modal__container ${saving ? '--saving' : ''}`}>
        {loading && <Loading center />}
        {!loading &&
          mentorList.length > 0 &&
          mentorList.map(mentor => {
            const isMentorInPlan = actualMentorsInPlan.includes(mentor.id);
            return (
              <li key={mentor.id} className="devplan-additem-modal__contentitem">
                <Checkbox
                  ariaLabel={mentor?.fullName}
                  value={mentor.id}
                  onChange={() => toggleMentorSelection(mentor.id)}
                  checked={selectedMentors.includes(mentor.id) || isMentorInPlan}
                  disabled={isMentorInPlan}
                />
                <div className="devplan-additem-modal__card type-mentor">
                  <HorizontalMentorshipCard
                    key={`mentor-${mentor?.id}`}
                    className="dev-plan__mentor_card"
                    id={mentor?.id}
                    userId={mentor?.id}
                    avatarUrl={mentor?.avatarImage}
                    name={mentor?.fullName}
                    position={mentor?.jobName && decodeURIComponent(mentor?.jobName)}
                    skills={mentor?.skillsDetail}
                    mentorCard={true}
                    customActions={[]}
                  />
                </div>
                <div className="devplan-additem-modal__card-actions">
                  <ButtonLink
                    target="_blank"
                    color="secondary"
                    variant="borderless"
                    padding="xsmall"
                    size="medium"
                    to={`/career/detail/${OPPORTUNITY_TYPE.MENTORSHIP}/${mentor?.id}`}
                    aria-label={translatr('web.common.main', 'GoToOpportunityDetailsPageForTitle', {
                      opportunity: omp('tm_tm_mentor'),
                      title: mentor?.fullName
                    })}
                    title={translatr('web.common.main', 'OpenInNewTab')}
                  >
                    <i className="icon-external-link" />
                  </ButtonLink>
                </div>
              </li>
            );
          })}
        {!loading && mentorList.length === 0 && (
          <li className="devplan-additem-modal__nocontent">
            <span>
              {translatr('web.talentmarketplace.development-plan', 'NoOpportunity', {
                opportunityType: omp('tm_tm_mentor')?.toLowerCase()
              })}
            </span>
            {currentStepData?.name &&
              !searchTerm &&
              translatr('web.talentmarketplace.development-plan', 'NoOpportunityForSkill', {
                opportunityType: omp('tm_tm_mentor')?.toLowerCase(),
                roleName: currentStepData?.name,
                roleVar: omp('tm_job_role')?.toLowerCase()
              })}
            {searchTerm &&
              translatr('web.talentmarketplace.development-plan', 'NoContentForFilteredKeyword')}
          </li>
        )}
        {!loading && loadingError && (
          <li className="error">
            {translatr('web.common.main', 'SorryWeCannotDisplayRequestedContent')}
          </li>
        )}
      </ul>
    </>
  );
};

export default MentorSearchResults;
