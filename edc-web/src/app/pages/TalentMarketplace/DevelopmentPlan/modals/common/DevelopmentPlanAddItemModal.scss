@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.devplan-additem-modal {
  .content {
    overflow: hidden;
  }

  .ed-dialog-modal-footer {
    justify-content: end !important;
  }

  .devplan-additem-modal__content {
    label {
      line-height: var(--ed-line-height-xs);
      margin-top: var(--ed-spacing-4xs);
      input {
        cursor: pointer;
      }
    }
    .error {
      margin: var(--ed-spacing-4xs) auto;
    }
    .devplan-additem-modal__results-header {
      font-size: var(--ed-font-size-sm);
      line-height: var(--ed-font-size-lg);
      margin: var(--ed-spacing-base) 0 var(--ed-spacing-4xs) 0;
      color: var(--ed-modal-title-color);
    }
    .devplan-additem-modal__container {
      margin: var(--ed-spacing-3xs) 0 0 0;
      overflow-x: visible;

      @include modern-scrollbars(6px, var(--ed-neutral-6), transparent, 5px);

      &.--saving {
        pointer-events: none;
        opacity: 0.6;
        animation: pulse-opacity 1s infinite;
      }

      li {
        & .dev-plan__project_card,
        & .dev-plan__mentor_card,
        & .tm__horizontal-project-card {
          position: unset !important;
        }
      }
    }
    .devplan-additem-modal__search-results {
      max-height: rem-calc(400);
      overflow-y: auto;
    }
    .devplan-additem-modal__search-description {
      font-size: var(--ed-font-size-2xs);
      color: var(--ed-gray-5);
    }

    .devplan-additem-modal__search-container {
      display: flex;
      position: relative;
      gap: var(--ed-spacing-4xs);
      align-items: stretch;

      .ed-search {
        flex: 1;
      }

      .filter-button {
        padding: var(--ed-spacing-4xs);
        font-size: var(--ed-font-size-md);
        color: var(--ed-gray-5);
      }
      .filter-counter {
        position: absolute;
        top: -6px;
        right: -6px;
        background-color: var(--ed-primary-base);
        color: var(--ed-white);
        border-radius: 50%;
        padding: var(--ed-spacing-4xs) var(--ed-spacing-3xs);
        font-size: var(--ed-font-size-2xs);
      }
    }

    .devplan-additem-modal__contentitem {
      display: flex;
      padding: 0;
      border-bottom: var(--ed-border-size-sm) solid var(--ed-gray-2);

      > label {
        margin-left: var(--ed-spacing-4xs);
      }

      &:first-child {
        padding-bottom: var(--ed-spacing-3xs);
      }

      &:not(:first-child):not(:last-child) {
        padding: var(--ed-spacing-3xs) 0;
      }

      &:last-child {
        padding-top: var(--ed-spacing-3xs);
      }
    }
    .devplan-additem-modal__card {
      &.type-content {
        pointer-events: none;
      }
      flex: auto;
    }
    .devplan-additem-modal__nocontent {
      display: flex;
      flex-direction: column;
      min-height: 200px;
      align-items: center;
      justify-content: center;
      color: var(--ed-gray-6);
      text-align: center;
      span {
        font-weight: var(--ed-font-weight-bold);
      }
    }
    .devplan-additem-modal__card-actions {
      margin-top: var(--ed-spacing-4xs);
    }
  }

  .devplan-additem-modal__counter {
    align-content: center;
    margin-right: var(--ed-spacing-4xs);
    font-weight: var(--ed-font-weight-normal);

    span {
      font-size: var(--ed-font-size-sm);
      font-weight: var(--ed-font-weight-black);
      padding-right: var(--ed-spacing-4xs);
      border-right: var(--ed-border-size-sm) solid var(--ed-border-color);
    }
  }
}
