import {
  bookmarkOpportunity,
  dismissOpportunity,
  unbookmarkOpportunity,
  undismissOpportunity,
  JOB_TYPE,
  CAPABILITY_STATUS
} from 'edc-web-sdk/requests/careerOportunities.v2';
import { track } from '@analytics/TrackWrapper';
import { omp, translatr } from 'centralized-design-system/src/Translatr';
import { Permissions } from '../../utils/checkPermissions';
import LD from '../../containers/LDStore';
import { TrackEvents, TrackEventProperties } from '@analytics/TrackEvents';
import { getCurrentUserLanguage } from '../Projects/ProjectForm/helpers';
import {
  getLevelByDecimal,
  noLevelPlaceholderOption
} from 'centralized-design-system/src/Utils/proficiencyLevels';
import { MAP_OLD_LEVELS_TO_DECIMAL } from '@components/modals/SkillsModal/constant';

export const MAP_TYPE = {
  [JOB_TYPE.ROLE]: 'role',
  [JOB_TYPE.VACANCY]: 'job'
};

export const OMP_URL_BY_TYPE = {
  [JOB_TYPE.ROLE]: 'job-roles',
  [JOB_TYPE.VACANCY]: 'job-vacancies'
};

export const sortOrder = {
  ASCENDING: 'asc',
  DESCENDING: 'desc'
};

export const CAPABILITY_STATUS_ORDER = [
  CAPABILITY_STATUS.DECLARED,
  CAPABILITY_STATUS.DETECTED,
  CAPABILITY_STATUS.MISSING
];

/*
 * Requirerments:
 * First two skills should be skills user has in profile
 * Second two skills should be skills the user might have
 * Third two skills should be skills the user doesn't have
 * Then show remaining skills user has;
 * Remaining skills user might have and remaining skills the user doesn't have up to max 7 skills,
 */
export const getRelatedSkills = (skillsHave, skillsMightHave, skillsNotHave) => [
  ...skillsHave.slice(0, 2),
  ...skillsMightHave.slice(0, 2),
  ...skillsNotHave.slice(0, 2),
  ...skillsHave.slice(2),
  ...skillsMightHave.slice(2),
  ...skillsNotHave.slice(2)
];
export const limitByCharsLength = (skills, maxCharsSpace = 20) => {
  const skillsLimitedByChars = [];
  let limit = maxCharsSpace;
  let isLimitReached = false;

  for (let i = 0; !isLimitReached && i < skills.length && limit > 0; i++) {
    const skill = skills[i];
    const skillLabel = skill.label || skill.name || skill.id;
    const skillLabelLength = skillLabel.length;
    if (skillLabelLength <= limit) {
      skillsLimitedByChars.push(skill);
      limit -= skillLabelLength;
    } else {
      isLimitReached = true;
    }
  }
  return skillsLimitedByChars;
};

/*
 * Sort skills by status order
 * Reverses the order introduced by getRelatedSkills method
 */
export const sortSkills = skills => {
  return [...skills].sort((a, b) =>
    CAPABILITY_STATUS_ORDER.indexOf(a.status) > CAPABILITY_STATUS_ORDER.indexOf(b.status) ? 1 : -1
  );
};

export const getSkillsByLevel = skills => {
  const result = {};
  const noLevelName = noLevelPlaceholderOption().value;
  skills.forEach(skill => {
    const levelName = getLevelByDecimal(
      skill.proficiencyLevel?.decimal || MAP_OLD_LEVELS_TO_DECIMAL[skill.level]
    )?.name;
    result[levelName || noLevelName] = [...(result[levelName || noLevelName] || []), skill];
  });
  return result;
};

export const APPLICATION = 'app';

export const PROJECT_COUNT_LIMIT = 15;

export const APPROVAL_REQUESTS_COUNT_LIMIT = 10;

const DEFAULT_MAX_ASPIRATIONAL_ROLES = 3;

export const MAX_LIMIT_ASPIRATIONAL_ROLES = 15;
export const MAX_LIMIT_PROJECTS = 15;
export const MAX_LIMIT_MENTORS = 15;

const isImpersonator = () =>
  LD.managerProxy() && ['user_dashboard', 'manager_dashboard'].includes(window?.__ED__?.proxyType);

const config =
  global?.__edOrgData?.configs?.find(f => f.name === 'talent_marketplace_config')?.value
    ?.talent_marketplace || {};

export const TALENT_MARKETPLACE_ENABLED = config.general?.configurations?.find(
  f => f.key === 'talent_marketplace'
)?.enabled;

export const TALENT_MARKETPLACE_JOB_ROLE_ENABLED = config.opportunities?.find(
  f => f.key === 'job_role'
)?.enabled;

export const TALENT_MARKETPLACE_JOB_VACANCY_ENABLED = config.opportunities?.find(
  f => f.key === 'job_vacancy'
)?.enabled;

export const TALENT_MARKETPLACE_PROJECT_ENABLED = config.opportunities?.find(
  f => f.key === 'project'
)?.enabled;

export const TALENT_MARKETPLACE_MENTORSHIP_ENABLED = config.opportunities?.find(
  f => f.key === 'mentorship'
)?.enabled;

export const TALENT_MARKETPLACE_SOURCING_ENABLED = config.opportunities?.find(
  f => f.key === 'sourcing'
)?.enabled;

export const LANDING_PAGE_V2_ENABLED = global?.__edOrgData?.configs?.find(
  f => f.name === 'landing_page_v2'
)
  ? global?.__edOrgData?.configs?.find(f => f.name === 'landing_page_v2')?.value
  : true;

export const PROJECTS_REQUIRE_APPLICATION_MESSAGE_ENABLED =
  config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_require_application_message')?.value || false;

export const PROJECTS_REQUIRE_MANAGER_CONSENT_ENABLED =
  config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_require_manager_consent')?.value || false;

export const PROJECTS_CHANGE_STATUS_ON_START_DATE =
  config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_change_status_on_start_date')?.value || false;

export const PROJECTS_REJECTION_REASON_ENABLED =
  config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_enable_rejection_reason_menu')?.value || false;

export const PROJECTS_REQUIRE_REJECTION_REASON =
  config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_require_rejection_reason')?.value || false;

export const MENTORS_REJECTION_REASON_ENABLED =
  config.opportunities
    ?.find(c => c.key === 'mentorship')
    ?.business_rules?.find(c => c.key === 'mentorship_enable_rejection_reason_menu')?.value ||
  false;

export const MENTORS_REQUIRE_REJECTION_REASON =
  config.opportunities
    ?.find(c => c.key === 'mentorship')
    ?.business_rules?.find(c => c.key === 'mentorship_require_rejection_reason')?.value || false;

export const MAX_ASPIRATIONAL_ROLES =
  config.aspirations?.max_aspiration_roles || DEFAULT_MAX_ASPIRATIONAL_ROLES;

export const TALENT_MARKETPLACE_ALL_JOB_TYPE_ENABLED =
  TALENT_MARKETPLACE_JOB_ROLE_ENABLED && TALENT_MARKETPLACE_JOB_VACANCY_ENABLED;

export const configListOfValues = config.general?.lovs
  ? JSON.parse(JSON.stringify(config.general.lovs))
  : {};

export const configListOfLabels = config.general?.labels;

export const isLovAvailableForOpportunityType = (lovKey, type, id) => {
  const lov = configListOfValues.find(item => item.key === lovKey);
  const isLovEnabled = lov?.entity?.enabled.includes(type) ?? false;

  return id ? isLovEnabled && lov?.values?.find(({ key }) => key === id)?.enable : isLovEnabled;
};
export const countDays = date => {
  const [day, month, year] = date.split('/');
  return Math.floor((new Date() - new Date(+year, month - 1, +day)) / (1000 * 60 * 60 * 24));
};

export const urlJobType = location => {
  const type = new URLSearchParams(location.search).get('type');
  const JOB_TYPE_ITEM = getJobTypeItems();
  if (TALENT_MARKETPLACE_ALL_JOB_TYPE_ENABLED) {
    return !type ? JOB_TYPE.VACANCY : JOB_TYPE_ITEM.find(itemType => itemType.id === type)?.id;
  }
  if (TALENT_MARKETPLACE_JOB_VACANCY_ENABLED && (!type || type === JOB_TYPE.VACANCY)) {
    return JOB_TYPE.VACANCY;
  }
  if (TALENT_MARKETPLACE_JOB_ROLE_ENABLED && (!type || type === JOB_TYPE.ROLE)) {
    return JOB_TYPE.ROLE;
  }
  if (TALENT_MARKETPLACE_PROJECT_ENABLED) {
    // support for project type should be added here
    return;
  }
  throw new Error('type param missing in URL');
};

export const DISMISS_ACTION = 'DISMISS';
export const BOOKMARK_ACTION = 'BOOKMARK';
export const ASPIRATIONAL_ACTION = 'ASPIRATIONAL';
const DISMISSED_PROPERTY_NAME = 'dismissed';
const BOOKMARKED_PROPERTY_NAME = 'bookmarked';
const ASPIRATIONAL_PROPERTY_NAME = 'aspirational';
export const CARD_OPERATIONS = {
  DISMISS_ACTION: DISMISSED_PROPERTY_NAME,
  BOOKMARK_ACTION: BOOKMARKED_PROPERTY_NAME,
  ASPIRATIONAL_ACTION: ASPIRATIONAL_PROPERTY_NAME
};

/* delay used when we need to reload data(from server) after action performed on cards (job and roles list with pagination only)
 * we need to delay backend call because of event driven architecture(we need add small amount of time to wait for event processing end)
 **/
export const CARDS_POST_OPERATION_RELOAD_DELAY = 1000;

const {
  DISMISS_ROLE,
  UNDISMISS_ROLE,
  DISMISS_VACANCY,
  UNDISMISS_VACANCY,
  MARK_ROLE_AS_ASPIRATIONAL,
  UNMARK_ROLE_AS_ASPIRATIONAL,
  BOOKMARK_OPPORTUNITY,
  UNBOOKMARK_OPPORTUNITY
} = TrackEvents.OMP;
const { ROLE_ID, ROLE_TITLE, COMPONENT, VACANCY_ID, VACANCY_TITLE } = TrackEventProperties.OMP;
export const toggleActionFactory = (
  type,
  action,
  state = [],
  setState,
  componentName = '',
  setIsActionPending = () => {}
) => (id, nextState, params) => {
  if (type !== JOB_TYPE.ROLE && type !== JOB_TYPE.VACANCY) {
    throw new Error('Invalid type applied to toggleActionFactory');
  }
  if (action !== DISMISS_ACTION && action !== BOOKMARK_ACTION) {
    throw new Error('Invalid action applied to toggleActionFactory');
  }
  if (!state || !setState) {
    throw new Error('Invalid state and setState applied to toggleActionFactory');
  }
  let performAction = () => Promise.resolve();
  let propertyName = () => Promise.resolve();
  let trackEventName = '';
  let { title, ...payloadParams } = params || {};
  const trackEventParams = {
    [type === JOB_TYPE.ROLE ? ROLE_ID : VACANCY_ID]: id,
    [type === JOB_TYPE.ROLE ? ROLE_TITLE : VACANCY_TITLE]: title,
    [COMPONENT]: componentName
  };
  if (type === JOB_TYPE.ROLE && action === DISMISS_ACTION) {
    performAction = nextState ? dismissOpportunity : undismissOpportunity;
    trackEventName = nextState ? DISMISS_ROLE : UNDISMISS_ROLE;
    propertyName = DISMISSED_PROPERTY_NAME;
  } else if ([JOB_TYPE.ROLE, JOB_TYPE.VACANCY].includes(type) && action === BOOKMARK_ACTION) {
    performAction = nextState ? bookmarkOpportunity : unbookmarkOpportunity;
    trackEventName = nextState ? BOOKMARK_OPPORTUNITY : UNBOOKMARK_OPPORTUNITY;
    propertyName = BOOKMARKED_PROPERTY_NAME;
  } else if (type === JOB_TYPE.VACANCY && action === DISMISS_ACTION) {
    performAction = nextState ? dismissOpportunity : undismissOpportunity;
    trackEventName = nextState ? DISMISS_VACANCY : UNDISMISS_VACANCY;
    propertyName = DISMISSED_PROPERTY_NAME;
  }
  return new Promise((resolve, reject) => {
    setIsActionPending(true);
    performAction(id, type, payloadParams) // eslint-disable-line sonarjs/no-extra-arguments
      .then(data => {
        if (data && !data.error) {
          if (Array.isArray(state)) {
            setState(
              state.map(record => {
                if (record.id !== id) {
                  return record;
                }
                return { ...record, [propertyName]: nextState };
              })
            );
          } else {
            setState({ ...state, [propertyName]: nextState });
          }
          track(trackEventName, trackEventParams);
          setIsActionPending(false);
          resolve();
        } else {
          setIsActionPending(false);
          reject(data);
        }
      })
      .catch(e => {
        setIsActionPending(false);
        reject(e);
      });
  });
};

export const toggleAspirationsFactory = (updateAspirationFn, componentName, state, setState) => (
  id,
  nextState,
  title,
  role = null
) => {
  return new Promise((resolve, reject) => {
    updateAspirationFn(id, role)
      .then(status => {
        if (Array.isArray(state)) {
          setState(
            state.map(record => {
              if (record.id !== id) {
                return record;
              }
              return { ...record, [ASPIRATIONAL_PROPERTY_NAME]: nextState };
            })
          );
        }
        track(nextState ? MARK_ROLE_AS_ASPIRATIONAL : UNMARK_ROLE_AS_ASPIRATIONAL, {
          [ROLE_ID]: id,
          [ROLE_TITLE]: title,
          [COMPONENT]: componentName
        });
        resolve(status);
      })
      .catch(error => {
        console.error(`Error in utils.toggleAspirationsFactory.func: ${error?.message}`);
        reject(error);
      });
  });
};

export const capitalize = label => {
  return label.charAt(0).toUpperCase() + label.slice(1);
};

export const getJobTypeItems = () => [
  {
    id: JOB_TYPE.ROLE,
    value: omp('tm_job_role')
  },
  {
    id: JOB_TYPE.VACANCY,
    value: omp('tm_job_vacancy')
  }
];

export const shouldAllowJobEdit = () =>
  TALENT_MARKETPLACE_JOB_VACANCY_ENABLED &&
  LD.showCareerGrowthTab() &&
  Permissions.has('MANAGE_CAREER_GROWTH');

export const shouldShowTMProject = () =>
  TALENT_MARKETPLACE_PROJECT_ENABLED &&
  LD.showCareerGrowthTab() &&
  Permissions.has('VIEW_PROJECT_OPPORTUNITIES') &&
  !isImpersonator();

export const shouldShowTMMentorship = () =>
  TALENT_MARKETPLACE_MENTORSHIP_ENABLED &&
  LD.showCareerGrowthTab() &&
  Permissions.has('VIEW_MENTORSHIP_OPPORTUNITIES') &&
  !isImpersonator();

export const shouldShowTMJobVacancy = () =>
  TALENT_MARKETPLACE_JOB_VACANCY_ENABLED &&
  LD.showCareerGrowthTab() &&
  Permissions.has('VIEW_JOB_VACANCY_OPPORTUNITIES');

export const shouldShowTMJobRole = () =>
  TALENT_MARKETPLACE_JOB_ROLE_ENABLED &&
  LD.showCareerGrowthTab() &&
  Permissions.has('VIEW_JOB_ROLE_OPPORTUNITIES');

export const shouldShowDevelopmentPlan = () =>
  shouldShowTMJobRole() && LD.isTransitionPlanEnabled();

export const shouldShowAtLeastOneTMContent = () => {
  return (
    shouldShowTMJobRole() ||
    shouldShowTMJobVacancy() ||
    shouldShowTMProject() ||
    shouldShowTMMentorship()
  );
};

export const shouldShowHomev2 = () => LANDING_PAGE_V2_ENABLED;

export const shouldAllowTMProjectCreate = () =>
  shouldShowTMProject() && Permissions.has('MANAGE_TM_PROJECTS');

export const shouldAllowTMMentorshipCreate = () =>
  shouldShowTMMentorship() && Permissions.has('CAN_BE_A_MENTOR');

export const shouldAllowToAddSkillsToPassport = () => Permissions.has('ADD_SKILLS');

export const FILTERED_PROJECT_TYPE = {
  ALL: 'all',
  APPLIED: 'applied',
  PENDING: 'pending',
  APPROVED: 'approved',
  INPROGRESS: 'inprogress',
  COMPLETED: 'completed',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn',
  ACCEPTED: 'accepted',
  PENDINGCONFIRMATION: 'pendingconfirmation',
  CONFIRMED: 'confirmed'
};

export const FILTERED_APPROVAL_TYPE = {
  ALL: 'all',
  PENDINGAPPROVAL: 'pendingapproval',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn'
};

export const ApprovalStatus = {
  APPROVED: 'APPROVED',
  PENDINGAPPROVAL: 'PENDINGAPPROVAL',
  REJECTED: 'REJECTED',
  PENDING: 'PENDING'
};

export const ApprovalActions = {
  APPROVE: 'APPROVE',
  REJECT: 'REJECT'
};

export const skillLabelsPreprocessor = () => {
  const skillConfig =
    global?.__edOrgData?.configs?.find(f => f.name === 'OrgCustomizationConfig')?.value?.web
      ?.labels || {};
  const userLanguage = getCurrentUserLanguage();
  const supportedLanguages = window?.__edOrgData?.languages || {};
  const language = supportedLanguages
    ? Object.keys(supportedLanguages)
        .find(key => userLanguage === supportedLanguages[key])
        ?.toLowerCase()
    : supportedLanguages;
  const configKeys = ['web/labels/beginner', 'web/labels/intermediate', 'web/labels/advanced'];
  let skillLabelArray = [];

  configKeys.forEach(key => {
    const defaultTranslation = skillConfig[key]?.defaultLabel
      ? translatr('web.common.main', skillConfig[key]?.defaultLabel)
      : skillConfig[key]?.defaultLabel;
    const SkillLabelToUse =
      language && skillConfig[key]?.languages && skillConfig[key]?.languages[language]
        ? skillConfig[key]?.languages[language]
        : skillConfig[key]?.label || defaultTranslation || skillConfig[key]?.defaultLabel;
    skillLabelArray.push([
      SkillLabelToUse,
      key.split('/')[2][0].toUpperCase() + key.split('/')[2].slice(1)
    ]);
  });
  return skillLabelArray;
};

export const shouldAllowUnsplashUpload = () => {
  const unsplashConfig = config.opportunities
    ?.find(c => c.key === 'project')
    ?.business_rules?.find(c => c.key === 'projects_allow_unsplash_upload');

  // disable unsplash upload by default
  if (!unsplashConfig) {
    return false;
  }

  return unsplashConfig.value;
};

export const mapFeaturesToConfiguration = features => {
  const configuration = {};
  features.forEach(feature => {
    configuration[feature.slug] = mapFlagsToConfiguration(feature.flags);
  });
  return configuration;
};

const mapFlagsToConfiguration = flags => {
  const flagConfiguration = {};
  flags.forEach(flag => {
    flagConfiguration[flag.slug] = flag.value;
  });
  return flagConfiguration;
};

export const CONFIG_SERVICE_CV_PARSING = {
  feature_slug: 'hr_data',
  flag_slug: 'cv_parsing'
};

export const OMP_CONFIG_SERVICE = {
  product_slug: 'lxp',
  service_slug: 'omp'
};

/* function to prevent reading live regions for a while (i.e. when popover is closed) */
export const preventReadLiveRegions = (time = 500) => {
  const ariaPoliteEls = document.querySelectorAll('[aria-live="polite"]');
  ariaPoliteEls.forEach(el => el.setAttribute('aria-live', 'off'));
  return setTimeout(
    () => ariaPoliteEls.forEach(el => el.setAttribute('aria-live', 'polite')),
    time
  );
};
