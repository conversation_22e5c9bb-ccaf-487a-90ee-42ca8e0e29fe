import React, { useState, useEffect, useMemo } from 'react';
import { object, func, string } from 'prop-types';
import { translatr } from 'centralized-design-system/src/Translatr/utils';
import Modal, {
  ModalContent,
  ModalFooter,
  ModalHeader
} from 'centralized-design-system/src/Modals';
import { connect } from 'react-redux';
import { close } from '@actions/modalActions';
import { tmSaveSearchFilters } from '@actions/talentmarketplaceActions';
import {
  cleanupFilterNames,
  extractFiltersIds
} from 'opportunity-marketplace/shared/filters/Filters.utils';
import './CareerPathingFilters.scss';
import JobFamilyFilters from './Filters/JobFamilyFilters';
import OrganizationFilters from './Filters/OrganizationFilters';
import { FILTERS_DEFAULT_ASSOCIATION_ID } from '../shared/filters/Filters.constants';
import { getSelectionForInput } from 'centralized-design-system/src/TreeView/utils';
import FocusLock from 'react-focus-lock';

const ROOT_CLASS = 'tm__careerpathing-filters';

const CareerPathingFilters = ({
  filtersState,
  closeModal,
  saveFilters,
  associationId = FILTERS_DEFAULT_ASSOCIATION_ID,
  currentUserLang,
  onApplyFilters = () => {}
}) => {
  const [selected, setSelected] = useState({});
  const [filters, setFilters] = useState(filtersState.filters[associationId] || {});
  const defaultFiltersIds = useMemo(() => extractFiltersIds(filters), []);

  useEffect(() => {
    const cleanedFilters = cleanupFilterNames(filters);
    setSelected(
      Object.fromEntries(
        Object.entries(cleanedFilters).map(([key, value]) => [key, value.length])
      ) || 0
    );
  }, [filters]);

  const markFilterOption = (filterIdPrefix, filterId, option, checked) => {
    // prefixing the id to maintain the sameorder between chips and filters
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    const { name, value } = option;

    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);

      // remove the old key first since the prefix can change
      const prevFilterKey = Object.keys(newFilters).find(key => key.includes(filterId));
      delete newFilters[prevFilterKey];

      newFilters[prefixedId] = [...(prevState[prevFilterKey] || [])];

      // cleaning filters so we can perform lookups by filter ids easier
      const cleanedNewFilters = cleanupFilterNames(newFilters);
      const existingFilter = cleanedNewFilters[filterId].find(fil => fil.value === value);
      if (existingFilter) {
        if (!checked) {
          newFilters[prefixedId] = cleanedNewFilters[filterId].filter(fil => fil.value !== value);
        } else {
          existingFilter.value = value;
        }
      } else {
        newFilters[prefixedId].push({
          name,
          value
        });
      }
      return newFilters;
    });
  };

  const updateFilterStateWithCheckedItems = (
    filterIdPrefix,
    filterId,
    filterLabel,
    selectedIds
  ) => {
    const prefixedId = `${filterIdPrefix}-${filterId}`;
    setFilters(prevState => {
      const newFilters = Object.assign({}, prevState);

      const prevFilterKey = Object.keys(newFilters).find(key => key.includes(filterId));
      delete newFilters[prevFilterKey];
      const filterData = filtersState?.config?.find(({ id }) => id === filterId)?.data;
      const newSelection = getSelectionForInput(filterData, new Set(selectedIds));
      newFilters[prefixedId] = newSelection.map(id => ({
        name:
          id === '0'
            ? `${filterLabel} ${translatr('web.common.main', 'All')}`
            : filterData?.find(el => el.id === id)?.name || '',
        value: id
      }));

      return newFilters;
    });
  };

  const applyFiltersHandler = () => {
    const newFiltersState = { ...filtersState };
    let newFilters = { ...filters };

    saveFilters({ ...newFiltersState, filters: { [associationId]: newFilters } }, associationId);
    onApplyFilters();
    closeModal();
  };

  const props = {
    filtersState,
    selected,
    filters,
    currentUserLang,
    defaultFiltersIds,
    markFilterOption,
    updateFilterStateWithCheckedItems
  };

  return (
    <Modal>
      <FocusLock
        onDeactivation={() => {
          setTimeout(() => {
            (
              document.getElementById('galaxy-view-filters-btn') ||
              document.querySelector('.cp-user-role__name')
            )?.focus();
          }, 100);
        }}
      >
        <ModalHeader
          title={translatr('web.talentmarketplace.main', 'AllFilters')}
          onClose={closeModal}
        />
        <ModalContent>
          <div className={ROOT_CLASS}>
            <JobFamilyFilters {...props} />
            <OrganizationFilters {...props} />
          </div>
        </ModalContent>
        <ModalFooter>
          <button className="ed-btn ed-btn-neutral" onClick={closeModal}>
            {translatr('web.talentmarketplace.main', 'Cancel')}
          </button>
          <button onClick={applyFiltersHandler} className="ed-btn ed-btn-primary">
            {translatr('web.talentmarketplace.main', 'Apply')}
          </button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

CareerPathingFilters.propTypes = {
  filtersState: object,
  closeModal: func,
  saveFilters: func,
  clearAllFilters: func,
  associationId: string,
  currentUserLang: string,
  onApplyFilters: func
};

const mapStateToProps = ({ currentUser, modal }) => ({
  currentUserLang: currentUser.get('profile')?.get('language'),
  filtersState: modal.get('filtersState'),
  associationId: modal.get('associationId'),
  onApplyFilters: modal.get('onApplyFilters')
});

const mapDispatchToProps = dispatch => ({
  closeModal: () => dispatch(close()),
  saveFilters: (newFilters, associationId) =>
    dispatch(tmSaveSearchFilters(newFilters, associationId))
});

export default connect(mapStateToProps, mapDispatchToProps)(CareerPathingFilters);
