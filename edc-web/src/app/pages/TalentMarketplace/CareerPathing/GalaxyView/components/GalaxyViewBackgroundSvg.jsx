import React from 'react';
import PropTypes from 'prop-types';
import GalaxyViewSectionLineSvg from './GalaxyViewSectionLineSvg';
import GalaxyLoadingStarsIcons from '../../../../../../app/icons/GalaxyLoadingStarsIcons';
import UserRoleSvg from 'opportunity-marketplace/shared/CareerPathing/UserRoleSvg';
import { getUsrAvatar } from 'opportunity-marketplace/CareerPathing/SubwayView/helpers';
import { prepareLoadingStar, USER_ROLE_PILL_WIDTH, USER_ROLE_PILL_HEIGHT } from '../helpers';
import { translatr } from 'centralized-design-system/src/Translatr';

const GalaxyViewBackgroundSvg = ({
  centerPoint,
  bgConfig,
  sectionsConfig,
  loading,
  radialDifference,
  zoom
}) => {
  const loadingStars = React.useMemo(
    () => [
      prepareLoadingStar(centerPoint, 0, radialDifference, zoom, 0, 'move1'),
      prepareLoadingStar(centerPoint, -135, radialDifference, zoom, 1, 'move2'),
      prepareLoadingStar(centerPoint, -65, radialDifference, zoom, 2, 'move3'),
      prepareLoadingStar(centerPoint, 110, radialDifference, zoom, 3, 'move4')
    ],
    [centerPoint, radialDifference, zoom]
  );

  return (
    <>
      {bgConfig.bgFillArray?.map(ring => (
        <circle
          className={`galaxy-view-bg__ring-nr-${ring.index}`}
          key={`galaxy-view-bg-ring-${ring.index}`}
          {...ring}
        />
      ))}
      {bgConfig.moveLines?.map((lineInfo, index) => (
        <g key={`galaxy-view-bg-moveline-${index}`}>
          <path
            id={`galaxy-view-bg-moveline-${index}`}
            d={`M${lineInfo.x1},${lineInfo.y1} ${lineInfo.x2},${lineInfo.y2}`}
          />
          <text>
            <textPath
              className="galaxy-view-bg-movieline__text"
              href={`#galaxy-view-bg-moveline-${index}`}
              startOffset={lineInfo.offset}
              textAnchor="end"
              aria-hidden="true"
            >
              {!loading && lineInfo.text}
            </textPath>
          </text>
        </g>
      ))}
      {sectionsConfig.length &&
        sectionsConfig.map((lineInfo, i) => (
          <GalaxyViewSectionLineSvg
            key={`galaxy-view-bg-section-line-${i}`}
            index={i}
            lineInfo={lineInfo.l}
            pathInfo={lineInfo.p}
          ></GalaxyViewSectionLineSvg>
        ))}

      {loading &&
        loadingStars.map(star => (
          <foreignObject
            style={{ transformOrigin: `${centerPoint}px ${centerPoint}px` }}
            className="--animate"
            x={star.x}
            y={star.y}
            width={star.size}
            height={star.size}
            key={`gv-loading-star-${star.svgName}`}
          >
            <GalaxyLoadingStarsIcons svgName={star.svgName} size={star.size} />
          </foreignObject>
        ))}
      {loading && (
        <UserRoleSvg
          usrImg={getUsrAvatar()}
          name={translatr('web.common.main', 'Loading')}
          width={150}
          height={100}
          pillWidth={USER_ROLE_PILL_WIDTH}
          pillHeight={USER_ROLE_PILL_HEIGHT}
          x={centerPoint}
          y={centerPoint}
        />
      )}
    </>
  );
};

GalaxyViewBackgroundSvg.propTypes = {
  centerPoint: PropTypes.number,
  bgConfig: PropTypes.object,
  sectionsConfig: PropTypes.array,
  loading: PropTypes.bool,
  radialDifference: PropTypes.number,
  zoom: PropTypes.number
};

export default GalaxyViewBackgroundSvg;
