import * as actionTypes from '../constants/actionTypes';

export function confirmation(
  title,
  message,
  callback,
  hideCancelBtn,
  confirmBtnTitle,
  isNegativeValue,
  hideConfirmBtn = false,
  cancelBtnTitle,
  anchorRef = null
) {
  return {
    type: actionTypes.OPEN_CONFIRMATION_MODAL,
    title,
    message,
    callback,
    hideCancelBtn,
    confirmBtnTitle,
    isNegativeValue,
    hideConfirmBtn,
    cancelBtnTitle,
    anchorRef
  };
}

export function openSAInviteModal() {
  return {
    type: actionTypes.OPEN_SA_INVITE_MODAL
  };
}

export function confirmationPrivateCardModal(
  title,
  message,
  isPrivate,
  callback,
  confirmBtnTitle,
  isNegativeValue
) {
  return {
    type: actionTypes.OPEN_PRIVATE_CARD_CONFIRMATION_MODAL,
    title,
    message,
    isPrivate,
    callback,
    confirmBtnTitle,
    isNegativeValue
  };
}

export function close(packType) {
  return dispatch => {
    if (packType === 'pathway') {
      dispatch({
        type: actionTypes.REMOVE_REORDER_CARD_IDS_PATHWAY
      });
    } else if (packType === 'journey') {
      dispatch({
        type: actionTypes.REMOVE_REORDER_CARD_IDS_JOURNEY
      });
    }
    dispatch({
      type: actionTypes.CLOSE_MODAL
    });
  };
}

export function openAssignModal(
  card,
  selfAssign,
  assignedStateChange,
  selfAssignedStateChange,
  anchorRef
) {
  return {
    type: actionTypes.OPEN_ASSIGN_MODAL,
    card,
    selfAssign,
    assignedStateChange,
    selfAssignedStateChange,
    anchorRef
  };
}

export function openAddToPathway(cardId, card, cardType, isAddingToJourney, openedFromHtmlElement) {
  return {
    type: actionTypes.OPEN_ADD_TO_PATHWAY_MODAL,
    cardId,
    card,
    cardType,
    isAddingToJourney,
    openedFromHtmlElement
  };
}

export function openAddToJourney(cardId) {
  return {
    type: actionTypes.OPEN_ADD_TO_JOURNEY_MODAL,
    cardId
  };
}

export function openInviteV2UserModal(isAdpUser) {
  return {
    type: actionTypes.OPEN_INVITE_V2_MODAL,
    isAdpUser: isAdpUser
  };
}

export function openCongratulationModal(userBadge) {
  return {
    type: actionTypes.OPEN_PATHWAY_CONGRATULATION_MODAL,
    userBadge
  };
}

export function openPostToChannelModal(card, openedFromHtmlElement) {
  return {
    type: actionTypes.OPEN_POST_TO_CHANNEL_MODAL,
    card,
    openedFromHtmlElement
  };
}

export function openPayPalSuccessModal(data) {
  return {
    type: actionTypes.OPEN_PAYPAL_SUCCESS_MODAL,
    paymentData: data
  };
}

export function openSkillsDirectoryModal(
  changeRole,
  role = '',
  isSettingsPage,
  isCareerPathPage = false
) {
  return {
    type: actionTypes.OPEN_SKILLS_DIRECTORY_MODAL,
    changeRole,
    role,
    isSettingsPage,
    isCareerPathPage
  };
}

export function openUserNameModal() {
  return {
    type: actionTypes.OPEN_USER_NAME_MODAL
  };
}

export function openUploadModal(uploadMeta) {
  return {
    type: actionTypes.OPEN_UPLOAD,
    uploadMeta
  };
}

export function openGtcConfirmationModal() {
  return {
    type: actionTypes.OPEN_GTC_CONFIRMATION_MODAL
  };
}

export function openUserSkillsAssessmentModal() {
  return {
    type: actionTypes.OPEN_USER_SKILLS_ASSESSMENT_MODAL
  };
}

export function openBecomeAMentorModal({
  editing,
  meta = {},
  onSuccessSaveProfile = () => {}
} = {}) {
  return {
    type: actionTypes.OPEN_BECOME_A_MENTOR_MODAL,
    editing,
    meta,
    onSuccessSaveProfile
  };
}

export function openBecomeAMentorConfirmationModal(mentorshipId) {
  return {
    type: actionTypes.OPEN_BECOME_A_MENTOR_CONFIRMATION_MODAL,
    mentorshipId
  };
}

export function openViewCommentModal(modalMetaData, onSuccess, durationModalConfig) {
  return {
    type: actionTypes.OPEN_VIEW_COMMENT_MODAL,
    modalMetaData,
    onSuccess,
    durationModalConfig
  };
}

export function openRejectionCommentModal(onReject, rejectionModalConfig, openedFromHtmlElement) {
  return {
    type: actionTypes.OPEN_REJECTION_COMMENT_MODAL,
    onReject,
    rejectionModalConfig,
    openedFromHtmlElement
  };
}

export function openAcceptMentorshipWithDurationModal(durationModalConfig) {
  return {
    type: actionTypes.OPEN_ACCEPT_MENTORSHIP_WITH_DURATION_MODAL,
    durationModalConfig
  };
}

export function openWithdrawCommentModal(withdrawModalConfig) {
  return {
    type: actionTypes.OPEN_WITHDRAW_COMMENT_MODAL,
    withdrawModalConfig
  };
}

export function openRequestMentorshipModal(id, name, onSuccess, mentorData, openedFromHtmlElement) {
  return {
    type: actionTypes.OPEN_REQUEST_MENTORSHIP_MODAL,
    id,
    name,
    onSuccess,
    mentorData,
    openedFromHtmlElement
  };
}

export function openSkillsModal({
  skills,
  modalType,
  onSkillAddedToPassport,
  openedFrom,
  skillsFrom,
  additionalProps,
  openedFromHtmlElement
}) {
  return {
    type: actionTypes.OPEN_SKILLS_MODAL,
    skills,
    modalType,
    onSkillAddedToPassport,
    openedFrom,
    skillsFrom,
    additionalProps,
    openedFromHtmlElement
  };
}

export function openOmpUserNotificationModal(modalData) {
  return {
    type: actionTypes.OPEN_OMP_USER_NOTIFICATION_MODAL,
    modalData
  };
}

export function openOMPShareModal(config) {
  return {
    type: actionTypes.OPEN_OMP_SHARE_MODAL,
    ...config
  };
}

export function openSimplifiedShareModal(config) {
  return {
    type: actionTypes.OPEN_SIMPLIFIED_SHARE_MODAL,
    ...config
  };
}

export function openViewMessageModal(sharersWithMessages) {
  return {
    type: actionTypes.OPEN_VIEW_MESSAGE_MODAL,
    sharersWithMessages
  };
}

export function openSubscribedUserModal(projectId, subscribedUsers, push) {
  return {
    type: actionTypes.OPEN_SUBSCRIBED_USER_MODAL,
    projectId,
    subscribedUsers,
    push
  };
}

export function openMatchingSkillsModal({
  skills,
  skillsFrom,
  onOpenSkillsModal,
  userId,
  hideSkillsAdditionLink,
  showCandidateLevelLabel,
  openedFromHtmlElement
}) {
  return {
    type: actionTypes.OPEN_MATCHING_SKILLS_MODAL,
    skills,
    skillsFrom,
    onOpenSkillsModal,
    userId,
    hideSkillsAdditionLink,
    showCandidateLevelLabel,
    openedFromHtmlElement
  };
}

export function openCareerPathFiltersModal(filtersState, associationId, onApplyFilters) {
  return {
    type: actionTypes.OMP_CAREERPATH_OPEN_FILTERS_MODAL,
    filtersState,
    associationId,
    onApplyFilters
  };
}

export function openMatchModal(
  jobId,
  jobType,
  scoreObject,
  isCareerPathEnabled,
  openedFromHtmlElement
) {
  return {
    type: actionTypes.OPEN_MATCH_MODAL,
    jobId,
    jobType,
    scoreObject,
    isCareerPathEnabled,
    openedFromHtmlElement
  };
}
export function openRecommendationFeedbackModal({
  thumbSelection,
  opportunityType,
  opportunities,
  focusReturnElement,
  onSubmit
}) {
  return {
    type: actionTypes.OPEN_RECOMMENDATION_FEEDBACK_MODAL,
    thumbSelection,
    opportunityType,
    opportunities,
    focusReturnElement,
    onSubmit
  };
}

export function openManagerSkillAssessmentModal(teamMembersList, assessmentType) {
  return {
    type: actionTypes.OPEN_MANAGER_SKILL_ASSESSMENT_MODAL,
    teamMembersList,
    assessmentType
  };
}

export function openManagerRecommendUpSkillModal(data) {
  return {
    type: actionTypes.OPEN_MANAGER_RECOMMEND_UPSKILL_MODAL,
    data
  };
}

export function openReviewAssessmentModal(reportee_id) {
  return {
    type: actionTypes.OPEN_REVIEW_ASSESSMENT_MODAL,
    reportee_id
  };
}

export function openCompleteYourProfileModal(active_tab, cb, activeExperienceId) {
  return {
    type: actionTypes.OPEN_COMPLETE_YOUR_PROFILE_MODAL,
    active_tab,
    cb,
    activeExperienceId
  };
}

export function openCompleteYourProfileModalv2({
  active_tab,
  openedFromHtmlElement,
  cb,
  activeExperienceId
}) {
  return {
    type: actionTypes.OPEN_COMPLETE_YOUR_PROFILE_MODAL,
    active_tab,
    openedFromHtmlElement,
    cb,
    activeExperienceId
  };
}

export function openApproversDetailsModal(data, openedFromHtmlElement) {
  return {
    type: actionTypes.OPEN_APPROVERS_DETAILS_MODAL,
    data,
    openedFromHtmlElement
  };
}

export function openCreateOpportunityAlertModal(data) {
  return {
    type: actionTypes.OPEN_CREATE_OPPORTUNITY_ALERT_MODAL,
    data
  };
}
