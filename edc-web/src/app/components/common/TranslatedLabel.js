/* eslint-disable sonarjs/prefer-immediate-return */
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';

let OrgLabels = {};
let OrgLanguages = {};

export function getTranslatedLabel(
  labelObj,
  profileLanguage,
  newDefaultLabel,
  prioritiseNewLabel = false,
  orgLanguages,
  skipLabelCheck
) {
  if (!labelObj) {
    return '';
  }
  if (labelObj.key && labelObj.key.indexOf('discover/carousel/customCarousel/') !== -1) {
    const customTranslation =
      labelObj?.translations?.find(item => {
        const fullLang =
          orgLanguages &&
          Object.keys(orgLanguages)
            .find(k => orgLanguages[k] === item.language)
            ?.toLowerCase();
        return fullLang === profileLanguage;
      })?.display_name || null;

    const { defaultLabel } = labelObj;
    const defaultLabelVal = prioritiseNewLabel ? newDefaultLabel : defaultLabel || newDefaultLabel;
    return customTranslation || tr(defaultLabelVal);
  }
  const translatedLabel = labelObj?.languages?.[profileLanguage]?.trim();
  const { label, defaultLabel } = labelObj;
  const customTranslation = translatedLabel || label;
  const defaultLabelVal = prioritiseNewLabel ? newDefaultLabel : defaultLabel || newDefaultLabel;
  /* the above condition is to display custom translations if custom label is added and
   * to show translation from csv when there is default label
   */
  return ((label || skipLabelCheck) && customTranslation) || tr(defaultLabelVal);
}

export function getNewTranslatedLabel({
  labelObj,
  appName,
  profileLanguage,
  newDefaultLabel,
  prioritiseNewLabel = false,
  orgLanguages,
  skipLabelCheck
}) {
  if (!labelObj) {
    return '';
  }
  if (labelObj.key && labelObj.key.indexOf('discover/carousel/customCarousel/') !== -1) {
    const customTranslation =
      labelObj?.translations?.find(item => {
        const fullLang =
          orgLanguages &&
          Object.keys(orgLanguages)
            .find(k => orgLanguages[k] === item.language)
            ?.toLowerCase();
        return fullLang === profileLanguage;
      })?.display_name || null;

    const { defaultLabel } = labelObj;
    let defaultLabelVal;
    if (prioritiseNewLabel) {
      defaultLabelVal = translatr(appName, newDefaultLabel);
    } else {
      defaultLabelVal = defaultLabel || translatr(appName, newDefaultLabel);
    }
    return customTranslation || defaultLabelVal;
  }
  const translatedLabel = labelObj?.languages?.[profileLanguage]?.trim();
  const { label, defaultLabel } = labelObj;
  const customTranslation = translatedLabel || label;
  const defaultLabelVal = prioritiseNewLabel ? newDefaultLabel : defaultLabel || newDefaultLabel;
  return (
    ((label || skipLabelCheck) && customTranslation) ||
    translatr(appName, defaultLabelVal.replace(/[' ]/g, '')) ||
    defaultLabelVal
  );

  /* the above condition is to display custom translations if custom label is added and
   * to show translation from csv when there is default label
   */
}

export function getProfileLanguage({ langs, currentUserLang }) {
  let profileLanguage;

  for (let prop in langs) {
    if (langs[prop] === currentUserLang) {
      profileLanguage = prop.toLowerCase();
      break;
    }
  }

  return profileLanguage;
}

export function setOrgConfig(config) {
  let key, temp;

  // Setup for Custom Config Labels
  const labels = config.OrgConfig?.web?.labels;
  if (labels) {
    for (key in labels) {
      temp = labels[key];
      OrgLabels[temp.defaultLabel.toLowerCase()] = {
        label: temp.label,
        ...temp.languages
      };
    }
  }

  // Setup for languages
  const langs = config.languages;
  if (langs) {
    for (key in langs) {
      OrgLanguages[langs[key]] = key.toLowerCase();
    }
  }
}

export function getLabel(label) {
  if (!label) {
    return '';
  }

  let profileLanguage = window.__ED__?.profile?.language || 'en';

  // Default
  let returnLabel = label;
  let labelKey = label.toLowerCase();
  // If label exists
  if (OrgLabels[labelKey]) {
    // Use the "label" or the default label
    returnLabel = OrgLabels[labelKey].label || returnLabel;

    if (profileLanguage !== 'en' && OrgLanguages[profileLanguage]) {
      // check to make sure a valid org language
      // update return label if translation exist
      returnLabel = OrgLabels[labelKey]?.[OrgLanguages[profileLanguage]] || returnLabel;
    }
  }

  return tr(returnLabel);
}
