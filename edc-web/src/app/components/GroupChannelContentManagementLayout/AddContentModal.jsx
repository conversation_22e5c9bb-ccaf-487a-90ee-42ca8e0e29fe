import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import <PERSON><PERSON>, { <PERSON><PERSON><PERSON>ead<PERSON>, ModalFooter } from 'centralized-design-system/src/Modals';
import { translatr } from 'centralized-design-system/src/Translatr';
import { tr } from 'edc-web-sdk/helpers/translations';
import FocusLock from 'react-focus-lock';
import { SearchInput } from 'centralized-design-system/src/Inputs';
import Spinner from '@components/common/spinner';
import { searchCardsInChannel, fetchChannelsWithTotal } from 'edc-web-sdk/requests/channels.v2';
import ChannelTileView from '../../pages/group/creation/ChannelTileView';
import { addItem } from 'edc-web-sdk/requests/carousels';
import { fetchCardForTeamAndChannel } from 'edc-web-sdk/requests/cards.v2';
import {
  getContentModalInAccessibleCardsMsg,
  getPlaceholderMessage,
  getTranslationModule
} from './utils';
import FeaturedCard from '../../pages/group/consumption/GroupConsumptionContent/FeaturedCard';
import classNames from 'classnames';
import { getTeamCards } from 'edc-web-sdk/requests/groups.v2';
import { open_v2 as openSnackBar } from '../../actions/snackBarActions';
import ShowMoreBtn from '@components/common/ShowMore';
import InaccessibleContentMsg from '@components/common/InaccessibleContentMsg';
import isLmsProviderEnabledInstance from '@utils/isLmsProviderEnabledInstance';
import debounce from 'centralized-design-system/src/Utils/debounce';
import getAccessibleContent from '@utils/fetchAccessibleCards';
import { adjustOffsetInArgs } from '@utils/fetchAccessibleCards/helpers';
import { useEffectAfterInitialMount } from 'centralized-design-system/src/Utils/hooks';
import { getContentTypeLabel } from '@utils/getContentTypeLabel';

const LIMIT = 10;
const GRP_CARDS_LIMIT = 20;

const AddContentModal = ({
  onClose,
  structureDetails,
  loadContentSectionData,
  isGroupContent,
  groupChannelId,
  existingContentIds,
  dispatch,
  isSubSection
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [offset, setOffset] = useState(0);
  const [isLoadMore, setIsLoadMore] = useState(false);
  const [selectedContent, setselectedContent] = useState(null);
  const [addableContent, setAddableContent] = useState([]);
  const [
    haveReceivedCardsAfterAccessibleChecks,
    setHaveReceivedCardsAfterAccessibleChecks
  ] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const totalCards = useRef(0);

  // This ref is required to track latest input value
  // Based on this we decide which is the valid resp to be stored in state
  const inputValRef = useRef(inputValue);

  const isChannel = structureDetails?.type === 'channels';
  const translationsModule = getTranslationModule(isSubSection);

  useEffect(() => {
    const searchInput = document.querySelector('.search-wrapper');
    searchInput?.addEventListener('click', handleSearchCLick);

    () => {
      searchInput?.removeEventListener('click', handleSearchCLick);
    };
  }, []);

  useEffectAfterInitialMount(() => {
    if (addableContent.length === 0 && totalCards.current > offset) {
      loadMoreData();
    } else if (isLoading || isLoadMore) {
      // Loaders for getCards, getGroupCards, searchCards API are stopped form here
      // because there are cases where we need to call the api again when the addable results
      // are already part of the section
      setIsLoading(false);
      setIsLoadMore(false);
    }
  }, [offset]);

  const handleSearchCLick = () => {
    setIsOpen(true);
  };

  const handleContentSelect = content => {
    setselectedContent(content);
    setIsOpen(false);
    setInputValue('');
  };

  useEffect(() => {
    inputValRef.current = inputValue;

    if (inputValue.length >= 3 || inputValue.length === 0) {
      setIsLoading(true);
      isChannel ? getAddableChannelList(inputValue) : !isGroupContent ? searchCards() : getCards();
    }
  }, [inputValue]);

  const handleSearchChange = value => {
    setOffset(0);
    setInputValue(value);
  };

  const getAddableChannelList = (value, isLoadingMore = false) => {
    let offsetAfterIterativeCallsOperation;
    fetchChannelsWithTotal({
      q: value,
      limit: 10,
      offset
    })
      .then(res => {
        totalCards.current = res.total;
        isLoadingMore
          ? setAddableContent(filterExistingContent([...addableContent, ...res.channels]))
          : setAddableContent(filterExistingContent(res.channels));
        setIsLoadMore(false);
        setIsLoading(false);
        setOffset(prevOffset =>
          offsetAfterIterativeCallsOperation === undefined
            ? prevOffset + LIMIT
            : offsetAfterIterativeCallsOperation + LIMIT
        );
      })
      .catch(error => {
        console.error(`Error in getAddableChannelList ${error}`);
        setIsLoading(false);
        setIsLoadMore(false);
      });
  };

  const areResultsFromLatestQuery = searchResultsFromQuery => {
    // Makes sure that the resp belongs to the latest query, if not do not execute further
    return searchResultsFromQuery === inputValRef.current;
  };

  const hasReceviedAccessibleCards = (res, filteredContent) => {
    // This means that resp has accessible cards but those are already added in the section
    return res.cards.length > 0 && filteredContent.length === 0;
  };

  const searchCards = async (isLoadingMore = false) => {
    try {
      const payload = {
        q: inputValue,
        limit: LIMIT,
        offset,
        skip_aggs: true,
        is_cms: false
      };

      let offsetAfterIterativeCallsOperation;

      const res = await getAccessibleContent({
        funcToCall: searchCardsInChannel,
        updateParentOffsetAfterOperation: latestOffset =>
          (offsetAfterIterativeCallsOperation = latestOffset),
        extractOffsetFromArgs: (...args) => args[1].offset,
        numberOfRequiredContent: LIMIT,
        isMounted: areResultsFromLatestQuery.bind(null, payload.q),
        adjustOffsetInArgs
      })(groupChannelId, payload);

      if (!isChannel && !areResultsFromLatestQuery(payload.q)) {
        return;
      }

      totalCards.current = res.total;

      const filteredContent = isLoadingMore
        ? filterExistingContent([...addableContent, ...res.cards])
        : filterExistingContent(res.cards);

      if (hasReceviedAccessibleCards(res, filteredContent)) {
        setHaveReceivedCardsAfterAccessibleChecks(true);
      }

      setAddableContent(filteredContent);
      setOffset(prevOffset =>
        offsetAfterIterativeCallsOperation === undefined
          ? prevOffset + LIMIT
          : offsetAfterIterativeCallsOperation + LIMIT
      );
    } catch (error) {
      console.error(`Error in  searchCards ${error}`);
      setIsLoading(false);
      setIsLoadMore(false);
    }
  };

  const getGroupCards = async (isLoadingMore = false) => {
    try {
      const payload = {
        limit: LIMIT,
        offset,
        type: 'all'
      };

      let offsetAfterIterativeCallsOperation;

      const res = await getAccessibleContent({
        funcToCall: getTeamCards,
        updateParentOffsetAfterOperation: latestOffset =>
          (offsetAfterIterativeCallsOperation = latestOffset),
        extractOffsetFromArgs: (...args) => args[1].offset,
        numberOfRequiredContent: LIMIT,
        isMounted: areResultsFromLatestQuery.bind(null, inputValue),
        adjustOffsetInArgs
      })(groupChannelId, payload);

      if (!areResultsFromLatestQuery(inputValue)) {
        return;
      }

      totalCards.current = res.total;

      const filteredContent = isLoadingMore
        ? filterExistingContent([...addableContent, ...res.cards])
        : filterExistingContent(res.cards);

      if (hasReceviedAccessibleCards(res, filteredContent)) {
        setHaveReceivedCardsAfterAccessibleChecks(true);
      }

      setAddableContent(filteredContent);
      setOffset(prevOffset =>
        offsetAfterIterativeCallsOperation === undefined
          ? prevOffset + LIMIT
          : offsetAfterIterativeCallsOperation + LIMIT
      );
    } catch (error) {
      setIsLoading(false);
      setIsLoadMore(false);
      console.error(`Error in  getGroupCards ${error}`);
    }
  };

  const getCards = async (isLoadingMore = false) => {
    if (!!inputValue) {
      try {
        const payload = {
          q: inputValue,
          limit: GRP_CARDS_LIMIT,
          offset,
          'team_ids[]': [groupChannelId]
        };

        let offsetAfterIterativeCallsOperation;

        const res = await getAccessibleContent({
          funcToCall: fetchCardForTeamAndChannel,
          updateParentOffsetAfterOperation: latestOffset =>
            (offsetAfterIterativeCallsOperation = latestOffset),
          numberOfRequiredContent: GRP_CARDS_LIMIT,
          isMounted: areResultsFromLatestQuery.bind(null, payload.q)
        })(payload);

        if (!areResultsFromLatestQuery(payload.q)) {
          return;
        }

        totalCards.current = res.total;

        const filteredContent = isLoadingMore
          ? filterExistingContent([...addableContent, ...res.cards])
          : filterExistingContent(res.cards);

        if (hasReceviedAccessibleCards(res, filteredContent)) {
          setHaveReceivedCardsAfterAccessibleChecks(true);
        }

        setAddableContent(filteredContent);
        setOffset(prevOffset =>
          offsetAfterIterativeCallsOperation === undefined
            ? prevOffset + GRP_CARDS_LIMIT
            : offsetAfterIterativeCallsOperation + GRP_CARDS_LIMIT
        );
      } catch (error) {
        setIsLoading(false);
        setIsLoadMore(false);
        console.error(`Error in  getCards ${error}`);
      }
    } else {
      getGroupCards(isLoadingMore);
    }
  };

  const filterExistingContent = data => {
    return data.filter(c => !existingContentIds.includes(c.id));
  };

  const loadMoreData = () => {
    setIsLoadMore(true);
    if (isChannel) {
      getAddableChannelList(inputValue, true);
    } else if (isGroupContent) {
      getCards(true);
    } else {
      searchCards(true);
    }
  };

  const addContent = () => {
    setIsAdding(true);
    let payload = {
      entity_id: selectedContent?.id,
      entity_type: isChannel ? 'Channel' : 'Card',
      structure_id: structureDetails?.id
    };
    selectedContent &&
      addItem(payload, structureDetails?.id)
        .then(() => {
          onClose();
          loadContentSectionData();
        })
        .catch(error => {
          const errMsg = error.message || error;
          const myErr = errMsg.includes('sync is pending')
            ? translatr('web.common.main', 'PendingContentSync') || errMsg
            : errMsg;
          console.error(myErr);
          dispatch(openSnackBar(myErr, 'error'));
          setIsLoading(false);
        });
  };

  const cardsPresentButInAccessibleToUser =
    isLmsProviderEnabledInstance() &&
    !isChannel &&
    totalCards.current > 0 &&
    addableContent.length === 0 &&
    !haveReceivedCardsAfterAccessibleChecks;

  return (
    <Modal size="small" className="content__modal">
      <FocusLock returnFocus={{ preventScroll: true }}>
        <ModalHeader
          title={
            isChannel
              ? translatr('web.common.main', 'AddChannelToSection')
              : translatr(
                  translationsModule,
                  isSubSection ? 'AddContentToSubSection' : 'AddContentToSection'
                )
          }
          onClose={onClose}
        />
        <div className="content__modal--body">
          <label className="add-channel-title">
            {isChannel
              ? translatr('web.common.main', 'Channel')
              : translatr('web.common.main', 'Content')}{' '}
            <span> * </span>{' '}
          </label>
          <label className="description-text">
            {isChannel
              ? translatr('web.common.main', 'SearchByChannelNameToFindAndAddChannelToThisSection')
              : translatr(
                  translationsModule,
                  isSubSection
                    ? 'SearchByCardNameToFindAndAddContentToThisSubSection'
                    : 'SearchByCardNameToFindAndAddContentToThisSection'
                )}
          </label>
          <div className="mt-8">
            <div className="search-wrapper">
              <SearchInput
                placeholder={getPlaceholderMessage(isChannel)}
                onSearch={debounce(handleSearchChange, 500)}
              />
            </div>
            {isOpen && (
              <div className="block addable-content-wrapper mt-8">
                {!isLoading ? (
                  <div
                    className={classNames('', {
                      'addable-channel-list': !!addableContent.length
                    })}
                  >
                    {!!addableContent?.length && (
                      <div className="channel-row channel-heading-row cursor-pointer">
                        <div className="channel-col">
                          <label>{translatr('web.common.main', 'Name')}</label>
                        </div>
                        <div className="channel-col">
                          <label>
                            {isChannel
                              ? translatr('web.common.main', 'Topics')
                              : translatr('web.common.main', 'Type')}
                          </label>
                        </div>
                      </div>
                    )}
                    {addableContent?.map((content, i) => {
                      const contentTypeLabel =
                        content?.readableCardType || content?.cardSubtype || content?.contentType;
                      return (
                        <button
                          key={i}
                          className="channel-row"
                          onClick={() => handleContentSelect(content)}
                        >
                          <div className="channel-col channel-text">
                            {isChannel
                              ? tr(content?.label)
                              : content?.cardTitle || content?.cardMessage}
                          </div>
                          <div className="channel-col channel-text">
                            {isChannel ? (
                              <>
                                {!!content?.topics?.length && (
                                  <label>
                                    <span>{tr(content?.topics[0]?.label)}</span>
                                    <span>{tr(content?.topics[1]?.label)}</span>
                                    <span>{tr(content?.topics[2]?.label)}</span>
                                    <span>...</span>
                                  </label>
                                )}
                              </>
                            ) : (
                              <label>
                                {getContentTypeLabel(contentTypeLabel) || contentTypeLabel}
                              </label>
                            )}
                          </div>
                        </button>
                      );
                    })}

                    {cardsPresentButInAccessibleToUser ? (
                      <InaccessibleContentMsg
                        msg={getContentModalInAccessibleCardsMsg(
                          totalCards.current > offset,
                          isGroupContent
                        )}
                        additionalClasses="m-padding font-size-xl"
                      />
                    ) : addableContent.length === 0 && !isLoading ? (
                      <div className="empty-state text-center mt-8">
                        {translatr('web.common.main', 'NoDataFound')}
                      </div>
                    ) : null}

                    {!isLoadMore && totalCards.current > offset && (
                      <ShowMoreBtn loadMoreData={loadMoreData} classNames="m-margin-top mb-12" />
                    )}

                    {!isLoading && isLoadMore && (
                      <div className="text-center mt-8">
                        <Spinner />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center mt-8 searching">
                    <Spinner />
                  </div>
                )}
              </div>
            )}
            {selectedContent && (
              <p className="black l-margin-top">
                {translatr('web.common.main', 'YouAreAboutToAddThisContent')}
              </p>
            )}
            {!isChannel && selectedContent ? (
              <div className="content-tile-view">
                <FeaturedCard card={selectedContent} />
              </div>
            ) : (
              selectedContent && <ChannelTileView channel={selectedContent} />
            )}
          </div>
        </div>
        <ModalFooter>
          <button className="ed-btn ed-btn-neutral" onClick={onClose}>
            {translatr('web.common.main', 'Close')}
          </button>
          <button
            className="ed-btn ed-btn-primary"
            disabled={isAdding || !selectedContent}
            onClick={addContent}
          >
            {isAdding
              ? translatr('web.common.main', 'Adding')
              : isChannel
              ? translatr('web.common.main', 'AddChannel')
              : translatr('web.common.main', 'AddContent')}
          </button>
        </ModalFooter>
      </FocusLock>
    </Modal>
  );
};

AddContentModal.propTypes = {
  onClose: PropTypes.func,
  structureDetails: PropTypes.object,
  loadContentSectionData: PropTypes.func,
  isGroupContent: PropTypes.bool,
  groupChannelId: PropTypes.string,
  existingContentIds: PropTypes.array,
  dispatch: PropTypes.func.isRequired,
  isSubSection: PropTypes.bool
};

export default AddContentModal;
