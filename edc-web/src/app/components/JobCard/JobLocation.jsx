import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { translatr, ompLov } from 'centralized-design-system/src/Translatr';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import { LOCATION_FIELDS, formatLocationByFields } from '@pages/TalentMarketplace/helpers';
import TextClamp from '@components/TextClamp';
import { isLovAvailableForOpportunityType } from 'opportunity-marketplace/util';

const createLocationLabel = (loc, availableLocations, countries, visibility, visibilityContext) => {
  if (availableLocations?.length > 0 && loc.locationId) {
    const locationLookup = availableLocations.find(
      itm => String(itm.id) === String(loc.locationId)
    );
    const city = locationLookup?.city || loc.city || '';
    const state = locationLookup?.state || loc.state || '';
    const country = locationLookup?.country || loc.country || '';
    const countryCode = locationLookup?.countryCode || loc.countryCode || '';

    return formatLocationByFields(
      {
        [LOCATION_FIELDS.NAME]: locationLookup?.location_name || loc.name,
        [LOCATION_FIELDS.STREET]: locationLookup?.street_address || loc.streetAddress,
        [LOCATION_FIELDS.ZIP]: locationLookup?.postal_code || loc.postalCode,
        [LOCATION_FIELDS.CITY]: city,
        [LOCATION_FIELDS.STATE]: state,
        [LOCATION_FIELDS.COUNTRY]: country,
        [LOCATION_FIELDS.COUNTRY_CODE]: countryCode
      },
      visibility,
      visibilityContext,
      countries
    );
  }

  return formatLocationByFields(
    {
      [LOCATION_FIELDS.NAME]: loc.name,
      [LOCATION_FIELDS.STREET]: loc.streetAddress,
      [LOCATION_FIELDS.ZIP]: loc.postalCode || loc.postCode,
      [LOCATION_FIELDS.CITY]: loc.city,
      [LOCATION_FIELDS.STATE]: loc.state || loc.region,
      [LOCATION_FIELDS.COUNTRY]: loc?.country,
      [LOCATION_FIELDS.COUNTRY_CODE]: loc.countryCode
    },
    visibility,
    visibilityContext,
    countries
  );
};

const JobLocation = ({
  locations,
  availableLocations,
  countries,
  mode,
  showMulitipleAsList = false,
  visibilityContext,
  locationsEnabled,
  locationFieldVisibility,
  lines = 4,
  jobId,
  type,
  titleId,
  locationIconId
}) => {
  const [mappedLocations, setMappedLocations] = useState([]);
  const noLocations = !mappedLocations?.length;
  const oneLocation = mappedLocations?.length === 1;
  const multipleLocations = mappedLocations?.length > 1;

  useEffect(() => {
    const newLocations = locations
      .sort((a, b) => (a.primary ? -1 : b.primary ? 1 : 0))
      .map(loc =>
        createLocationLabel(
          loc,
          availableLocations,
          countries,
          locationFieldVisibility,
          visibilityContext
        )
      )
      .filter(l => !!l);
    setMappedLocations([...new Set(newLocations)]); // remove duplicated strings
  }, [locations, availableLocations, locationsEnabled, visibilityContext, countries]);

  const renderLocationList = useCallback((loc, mod, extraClass = '', separator = '') => {
    const isModEnabled = isLovAvailableForOpportunityType('workplace_model', type, mod);
    return (
      <TextClamp line={lines}>
        {isModEnabled && !!mod && (
          <div className={extraClass}>({ompLov('workplace_model', mod)}) </div>
        )}
        {loc.map((l, key) => (
          <div key={key} id={`location-for-${jobId}`} className={extraClass}>
            {l}
            {separator}
          </div>
        ))}
      </TextClamp>
    );
  }, []);

  if (noLocations) return translatr('web.common.main', 'NotSpecified');
  if (oneLocation) return renderLocationList(mappedLocations, mode, 'd-inline');
  if (multipleLocations) {
    const label = mappedLocations[0];
    const restLocations = mappedLocations.slice(1);
    const tooltip = `<ul>${restLocations.map(el => '<li>' + el + '</li>').join('')}</ul>`;
    const scTooltip = safeRender(
      restLocations
        .map(
          (el, idx) =>
            `${translatr('web.talentmarketplace.main', 'LocationX', {
              no: idx + 1
            })} ${el}`
        )
        .join(', ')
    );

    return (
      <>
        {showMulitipleAsList && renderLocationList(restLocations, mode, '', '; ')}
        {!showMulitipleAsList && (
          <div>
            <TextClamp line={lines}>{label}</TextClamp>
            <Tooltip
              message={tooltip}
              pos="top"
              isHtmlIncluded={true}
              customClass={`joblocation--tooltip-locations`}
              arialabelledby={`${titleId} ${locationIconId} ${jobId}__button`}
              ariaDescribedby={`${jobId}__tooltip-to-remove`}
              tabIndex={0}
              tooltipParentRole={'button'}
            >
              <>
                <span
                  id={`${jobId}__button`}
                  className="joblocation--view-more ed-btn-v2 ed-btn-secondary-borderless-v2"
                >
                  {translatr('web.talentmarketplace.main', 'ViewRemainingMore', {
                    remaining: restLocations.length
                  })}
                </span>
                <div
                  id={`${jobId}__tooltip-to-remove`}
                  className="sr-only"
                  dangerouslySetInnerHTML={{ __html: scTooltip }}
                />
              </>
            </Tooltip>
          </div>
        )}
      </>
    );
  }
};

const mapStoreStateToProps = ({ availableLocations, locationsConfiguration }) => {
  return {
    availableLocations: availableLocations.get('availableLocations'),
    locationsEnabled: locationsConfiguration.get('enable'),
    locationFieldVisibility: locationsConfiguration.get('visibility')
  };
};

JobLocation.propTypes = {
  locations: PropTypes.array,
  countries: PropTypes.array,
  availableLocations: PropTypes.array,
  locationsEnabled: PropTypes.bool,
  mode: PropTypes.string,
  showMulitipleAsList: PropTypes.bool,
  visibilityContext: PropTypes.string,
  locationFieldVisibility: PropTypes.object,
  lines: PropTypes.number,
  jobId: PropTypes.string,
  type: PropTypes.string,
  titleId: PropTypes.string,
  locationIconId: PropTypes.string
};

export default connect(mapStoreStateToProps)(JobLocation);
