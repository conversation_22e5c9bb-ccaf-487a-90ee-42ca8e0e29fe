@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

.ed-ui {
  .ed-carousel-container {
    .ed-carousel {
      margin: 0 0 var(--ed-spacing-base) 0;
    }

    &.right-scroll {
      .scroll-btn.right {
        right: rem-calc(5);
      }
    }

    &.left-scroll {
      .scroll-btn.left {
        left: rem-calc(5);
      }
    }
  }
}

#role-card-carousel-tooltip {
  white-space: normal;
}

.role-card-popper {
  .common-menu-styles {
    width: auto;
    box-shadow: none;
    li {
      display: flex;
      align-items: stretch;
      padding: 0;

      button {
        padding: 0 var(--ed-spacing-base);
        width: 100%;
        text-align: left;
        cursor: pointer;

        &:hover {
          background-color: var(--ed-state-hover-bg-color-primary);
        }
      }
    }
  }
  .dropdown-btn {
    display: inline-block;
  }
  width: auto;
  [role='menu'] {
    margin-left: 0;
    padding-top: 8px;
    padding-bottom: var(--ed-spacing-2xs);
  }
  .menu-item-disabled {
    color: var(--ed-text-color-supporting);
  }
}

.role-card-compact {
  width: 100%;
  padding: var(--ed-spacing-2xs);
  background-color: var(--ed-white);
  border-radius: var(--ed-border-radius-md);

  &.role-card-selected {
    outline: var(--ed-border-size-md) solid var(--ed-primary-base);
  }

  .role-card_header {
    color: var(--ed-gray-7);
    font-size: var(--ed-font-size-base);
    line-height: var(--ed-line-height-base);
  }

  .role-card_family {
    font-size: var(--ed-font-size-sm);
    color: var(--ed-gray-6);
  }

  .role-card_main {
    margin-top: var(--ed-spacing-4xs);

    & > div {
      margin-bottom: var(--ed-spacing-4xs);
    }
  }

  .role-card_header,
  .role-card__details {
    height: auto;
  }

  .role-card__details {
    gap: var(--ed-spacing-4xs);
  }

  .icon {
    display: none;
  }

  &_main {
    margin-top: 0;
    > div {
      margin-bottom: 0;
    }
  }

  &:hover {
    box-shadow: none;
  }
}

.role-card {
  width: rem-calc(288);
  padding: var(--ed-spacing-base);
  background-color: var(--ed-body-bg-color);
  border: var(--ed-border-size-sm) solid var(--ed-neutral-6);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-shrink: 0;

  .icon {
    margin: 0 0.5rem 0 0;
  }

  &-full {
    color: var(--ed-text-color-supporting);
    border-radius: var(--ed-border-radius-lg);
    border: none;
    margin: 0;
    box-shadow: var(--ed-shadow-base);
    transition: box-shadow ease 0.25s;
    padding: var(--ed-spacing-base);
    height: 337px;
    &:hover {
      box-shadow: var(--ed-card-shadow-hover);
    }
    .role-card_main {
      margin-top: var(--ed-spacing-base);
    }
  }
  &--dismissing {
    opacity: 0.5;
  }

  &__details {
    height: 158px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    cursor: pointer;
  }

  .role-card-full &__details {
    height: 230px;
  }

  &.role-card-full &__matching {
    padding-top: rem-calc(16);
    padding-bottom: rem-calc(6);
    margin-top: var(--ed-spacing-4xs);
    border-top: var(--ed-border-size-sm) solid var(--ed-gray-2);
  }

  &__extra {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }

  &__skills {
    display: flex;
    font-size: var(--ed-font-size-supporting);
    &__icon {
      flex: 0 0 auto;
      font-size: var(--ed-font-size-base);
    }
    .skill-matched {
      color: var(--ed-primary-base);
    }
  }

  &__vacancy {
    display: flex;
    align-items: center;
    margin-bottom: var(--ed-spacing-2xs);
    font-size: var(--ed-font-size-supporting);
    .role-card__icon {
      color: var(--ed-primary-base);
      position: relative;

      &__decorator {
        position: absolute;
        right: 1px;
        top: -2px;
        width: 8px;
        height: 8px;

        div {
          height: 1px;
          width: 40%;
          position: absolute;
          background-color: var(--ed-primary-base);
        }

        &--line1 {
          transform: rotate(-95deg);
          top: 50%;
          left: -50%;
        }
        &--line2 {
          transform: rotate(-45deg);
          top: 50%;
        }
        &--line3 {
          bottom: 0;
          transform: rotate(5deg);
        }
      }
    }
  }

  &__icon {
    flex: 0 0 auto;
  }

  &:hover {
    box-shadow: var(--ed-card-shadow-hover);
  }

  .active {
    color: var(--ed-primary-base);
  }

  &_main {
    margin-top: var(--ed-spacing-2xs);
    > li {
      margin-bottom: var(--ed-spacing-2xs);
      min-height: rem-calc(24);
      line-height: 1rem;
    }
    .icon {
      font-size: var(--ed-font-size-base);
    }
    .role-level,
    .role-area,
    .role-location {
      color: var(--ed-text-color-supporting);
      font-size: var(--ed-font-size-sm);
      &--multi {
        width: 100%;
        overflow: hidden;
      }
    }
    .role-location div {
      line-height: 1.2rem;
    }
    & > li {
      display: flex;
      align-items: center;
    }
  }

  .role-card_helper-text {
    color: var(--ed-text-color-supporting);
    font-size: var(--ed-font-size-sm);
  }

  &_header {
    display: flex;
    justify-content: space-between;
    height: 47px;
    position: relative;

    .role-title {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      text-align: left;
      color: var(--ed-gray-6);
      font-weight: var(--ed-font-weight-semibold);
      overflow: hidden;
      line-height: calc(var(--ed-line-height-base) * 1rem);

      &:hover,
      &:focus {
        text-decoration: underline;
        color: var(--ed-gray-6);
      }

      h3 {
        font-size: var(--ed-font-size-base) !important;
      }

      &.size-medium h3 {
        font-size: var(--ed-font-size-sm) !important;
        line-height: calc(var(--ed-line-height-sm) * 1rem);
      }
    }
    .role-card__smile-icon {
      width: rem-calc(40);
    }

    .role-menu {
      > div {
        display: flex;
        justify-content: flex-end;
      }
      .icon {
        font-size: 20px;
      }
      .card-icon {
        font-size: 1.6875rem;
        line-height: 0 !important;
        vertical-align: sub;
        margin: 0 0 0 5px;
      }
      .menu-item-disabled {
        color: var(--ed-text-color-supporting);
      }
      .ed-dropdown {
        margin-right: 0;
      }
    }
  }

  &_footer {
    color: var(--ed-text-color-supporting);
    font-size: var(--ed-font-size-sm);
    .icon {
      font-size: var(--ed-font-size-xl);
    }

    .bold {
      font-weight: var(--ed-font-weight-semibold);
    }
    .role-progress-description {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: calc(var(--ed-line-height-base) * 1rem);
    }
    .role-progress-bar {
      margin: var(--ed-spacing-2xs) 0;
    }
  }

  &.empty-role-card {
    height: rem-calc(340);
    padding: rem-calc(32);
    text-align: center;
    font-size: var(--ed-font-size-base);
    display: flex;
    flex-direction: column;
    justify-content: center;

    &.small {
      height: auto;
    }

    .role-card_main {
      background-image: linear-gradient(
        to right,
        var(--ed-border-color) 33%,
        rgba(255, 255, 255, 0) 0%
      );
      background-position: bottom;
      background-size: 6px 1px;
      background-repeat: repeat-x;
      margin-bottom: rem-calc(15);
      padding-bottom: rem-calc(18);
      & > * {
        color: var(--ed-border-color);
      }
      .role-title {
        .icon {
          font-size: var(--ed-font-size-3xl);
        }
        p {
          margin-top: 5px;
        }
        .icon-wrapper {
          flex-grow: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: rem-calc(30);

          .icon-briefcase-thin {
            font-size: rem-calc(40);
            padding-right: 0;
            margin-right: 0;
          }
          .icon-map-marker-question {
            font-size: rem-calc(18);
            margin-left: 0;
            padding-right: 0;
            margin-bottom: rem-calc(24);
          }
        }
      }
    }

    .role-card_helper-text > * {
      font-size: var(--ed-font-size-sm);
      color: var(--ed-text-color-supporting);
      &.role-card-link {
        color: var(--ed-primary-base);
        text-decoration: underline;
      }
    }
  }
  & &__matching {
    padding-top: var(--ed-spacing-3xs);
    padding-bottom: var(--ed-spacing-3xs);
    font-size: var(--ed-font-size-sm);
  }
}

.block {
  .role-card {
    width: rem-calc(286);
    border: var(--ed-border-size-sm) solid var(--ed-gray-2);
    box-shadow: none;
  }
  .role-carousel-body {
    .ed-carousel {
      gap: var(--ed-spacing-2xs);
    }
    .right-scroll .scroll-btn.right {
      right: calc(-1 * var(--ed-spacing-base)) !important;
    }
    .left-scroll .scroll-btn.left {
      left: calc(-1 * var(--ed-spacing-base)) !important;
    }
  }
  .ed-carousel {
    .gc-loading-dynamic {
      border: var(--ed-border-size-sm) solid var(--ed-gray-2);
      box-shadow: none;
      width: rem-calc(286) !important;
    }
  }
}
