@import '~styles/_base.scss';
@import '~centralized-design-system/src/Styles/_variables.scss';

// Common css for all metadata components
.picasso-card-metadata-wrapper {
  margin-top: rem-calc(6);
  margin-bottom: var(--ed-spacing-base);

  .external-metadata-container {
    font-family: 'Open Sans';
    font-size: var(--ed-font-size-sm);
    color: var(--ed-text-color-supporting);
  }

  .pricing-section-wrapper {
    font-size: var(--ed-font-size-base);
    color: var(--ed-text-color-primary);
  }

  .actions-section-wrapper {
    margin-bottom: var(--ed-spacing-base);

    > button:last-of-type {
      margin-right: var(--ed-spacing-xs);
    }
    .icon-redo-alt-arrow::before {
      color: var(--ed-text-color-primary);
      font-size: var(--ed-font-size-base);
    }
  }

  .social-activity-and-price-wrapper {
    margin-top: var(--ed-spacing-base);
    i.discount-validity-date {
      color: var(--ed-warning-2);
    }
  }

  .social-activity-label-details {
    .card-std-count-container {
      font-size: rem-calc(13);
      color: var(--ed-text-color-supporting);

      .card-std-count {
        color: var(--ed-text-color-primary);
        font-weight: var(--ed-font-weight-normal);
      }
    }

    .no-social-activity {
      height: 100%;
      align-items: flex-end;
      font-size: rem-calc(13);
      color: var(--ed-text-color-supporting);
    }
  }

  .star-rating-container {
    // Styling for ratings on tile view
    .star-ratings {
      .star-container {
        transform: translateY(rem-calc(2));
      }
      pointer-events: none;
    }

    .metadata {
      &.card-std-count {
        color: var(--ed-primary-base);
        margin-left: rem-calc(2);
      }
    }
  }

  .ext-pr-metadata-wrapper,
  .metadata {
    &.channel-metadata {
      .icon-channel,
      .metadata-label {
        margin-top: rem-calc(3);
      }
    }

    .metadata-label {
      font-size: var(--ed-font-size-supporting);
      color: var(--ed-text-color-supporting);
      margin-right: var(--ed-spacing-2xs);
      flex-shrink: 0;
    }

    &.skills-wrapper {
      margin-top: rem-calc(5);
    }

    .skills {
      font-size: var(--ed-font-size-supporting);
    }
  }

  .card-metadata-icon {
    margin-left: 0;
    font-size: rem-calc(13);
    color: var(--ed-text-color-supporting);
    margin-right: rem-calc(3);
  }

  .no-border-btn {
    padding-right: 0;
  }
}

// Specific css for tile view metadata
.card-std-tile {
  .picasso-card-metadata-wrapper {
    .external-metadata-wrapper {
      max-width: rem-calc(171.5);

      // This styling is to show space between the tooltip text which are generated serverside
      &.text-ellipsis + span {
        & div > span {
          margin-right: rem-calc(5);
        }
      }
    }

    .tile-view-with-cpe-credits {
      max-width: rem-calc(150);
    }

    margin-bottom: rem-calc(13);
    margin-top: 0;

    .social-activity-and-price-wrapper {
      height: rem-calc(22);

      .social-activity-label-details {
        .card-std-count-container {
          font-size: var(--ed-font-size-sm);
          margin-right: var(--ed-spacing-2xs);
        }

        .no-social-activity {
          font-size: var(--ed-font-size-sm);
        }
      }
    }

    .cpe-credit {
      &.after-separator {
        &::after {
          content: '';
        }
      }
    }

    .fit-cpe {
      white-space: nowrap;
      padding-left: rem-calc(5);
    }
  }
}

// Specific css for tile view feed view
.card-std-bigcard {
  .picasso-card-metadata-wrapper {
    margin-top: rem-calc(13);

    .social-activity-and-price-wrapper {
      min-height: rem-calc(22);
    }

    .external-metadata-wrapper {
      width: rem-calc(400);
    }
  }
}

.mkp-discount-details {
  .discounted-price {
    color: var(--ed-primary-base);
  }

  .actual-price {
    color: var(--ed-black);
  }

  .discount-validity-date {
    color: var(--ed-warning-2);
    i {
      font-size: var(--ed-font-size-base);
    }
  }
}

// Specific css for standalone view metadata
.stand-alone {
  .price-and-mark-as-complete-btn-wrapper {
    margin-bottom: 0;
  }

  .standalone-metadata {
    > div {
      &:first-child {
        margin-top: rem-cal(25);
      }
    }
  }
}

.standalone-metadata-wrapper {
  margin-top: var(--ed-spacing-base);
  margin-bottom: rem-calc(12);
  border-bottom: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);

  .standalone-metadata {
    color: var(--ed-text-color-supporting);

    @include min-screen-width($breakpoint-md) {
      color: var(--ed-text-color-primary);
    }

    .star-ratings {
      // Styling for ratings on Standalone view
      .star-container {
        .star {
          stroke: var(--ed-text-color-supporting);
          stroke-width: rem-calc(1);
        }
      }
    }

    .metadata-label {
      display: none;

      @include min-screen-width($breakpoint-md) {
        display: inline;
      }
    }

    .channel-metadata,
    .standalone-contributor-container,
    .team-container {
      .flex-wrap {
        width: calc(100% - 1.5rem);
      }
    }

    .card-metadata-icon {
      font-size: var(--ed-font-size-lg);
      margin-right: var(--ed-spacing-xs);

      &.icon-channel {
        margin-right: rem-calc(14);
        font-size: rem-calc(21);
      }

      &.icon-settings {
        margin-right: var(--ed-spacing-sm);
      }
    }

    .icon-tags {
      font-size: rem-calc(18);
    }

    .tag-container {
      margin-top: var(--ed-spacing-2xs);

      .tag-wrapper {
        font-size: var(--ed-font-size-sm);
      }
    }

    .team-container {
      margin-top: var(--ed-spacing-2xs);
    }

    > div {
      &:nth-child(1) {
        margin-top: var(--ed-spacing-xl);
      }

      &:last-child {
        margin-bottom: var(--ed-spacing-base);
      }
    }
  }

  .card-metadata-icon {
    font-size: var(--ed-font-size-base);
  }

  .standalone-contributor-container {
    margin-top: var(--ed-spacing-xs);
    .contributor-name-label {
      word-break: normal;
      .icon-collaborators {
        margin-left: 0;
        color: var(--ed-text-color-supporting);
        margin-right: rem-calc(7);
      }
    }
  }

  .flex-direction-row {
    flex-direction: row !important;
  }

  .price-and-mark-as-complete-btn-wrapper {
    flex-direction: column;
    align-items: flex-end;

    .standalone-price-bottom-margin {
      margin-bottom: var(--ed-spacing-base);
    }

    .pricing-section-wrapper {
      .card-std-promotion {
        color: var(--ed-info-5);
      }

      .icon-check {
        color: var(--ed-state-active-color);
        font-weight: var(--ed-font-weight-semibold);
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .external-metadata-container {
      display: block;

      .price-and-mark-as-complete-btn-wrapper {
        align-items: flex-start;

        .mark-as-complete-button-tooltip {
          align-self: flex-start;
        }

        .picasso-mark-as-complete-button {
          margin-top: var(--ed-spacing-base);
          margin-left: 0;

          .social-activity-icon-label {
            display: inline;
          }
        }

        .pricing-section-wrapper {
          margin-top: var(--ed-spacing-base);
          margin-bottom: 0;
        }
      }
    }
  }

  &::before {
    border-top: var(--ed-border-size-sm) solid var(--ed-state-disabled-color);
    content: '';
    display: block;
    margin-left: rem-calc(-16);
    margin-right: calc(-1 * var(--ed-spacing-base));
    padding-bottom: var(--ed-spacing-base);
  }

  .external-metadata-container {
    .progress-status-bar {
      .progress-container {
        display: flex;
        align-items: center;

        .progress-value {
          width: 2rem;
        }
        .progress {
          width: rem-calc(82);
        }
      }
    }
  }

  .external-metadata-and-actions-container {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    justify-content: space-between;
    max-height: rem-calc(40);
    .vilt-registration-action-container {
      display: flex;
      flex-wrap: nowrap;
      flex-direction: row;
      align-items: center;
      .registered-vilt {
        color: var(--ed-primary-base);
        padding: rem-calc(11) rem-calc(20);
        .icon-check {
          margin: 0 rem-calc(6) 0 0;
        }
      }
      .unregister-vilt {
        color: var(--ed-primary-base);
        a {
          color: var(--ed-primary-base);
        }
        .unregister-btn {
          text-decoration: underline;
          padding: rem-calc(13) rem-calc(20);
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .external-metadata-and-actions-container {
      max-height: none;
    }
  }

  .extra-metadata-and-actions-section {
    max-height: 4rem;
  }
}

.pathway-std-additional-metadata {
  padding: var(--ed-spacing-base);
  box-shadow: var(--ed-shadow-base);

  .standalone-metadata-wrapper {
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: none;

    &::before {
      display: none;
    }
  }
}

.ed-btn-negative {
  .social-activity-icon-label {
    color: var(--ed-text-color-error);
  }
}

[dir='rtl'] {
  .social-activity-label-details .card-std-count {
    float: right;
  }
}
