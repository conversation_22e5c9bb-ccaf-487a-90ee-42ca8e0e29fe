import LD from '../../../../../app/containers/LDStore';
import validateIfVimeoVideo from './validateIfVimeoVideo';
import validateIfYoutubeVideo from './validateIfYoutubeVideo';
import isBigCardLMSContent from './isBigCardLMSContent';
import { CARD_LAYOUT } from '@utils/constants';

const fetchEmbedHtmlDetails = (card, cardType, layout, showInline) => {
  const { resource } = card;
  const { embedHtml } = resource || {};

  // if any of LD.isEmbedHtmlPlayInlineEnabled() or (resource?.embedHtml || resource) is false we don't show inline Embed payer
  // or if  layout === 'Tile' we don't show inline Embed payer
  if (
    !(LD.isEmbedHtmlPlayInlineEnabled() && (resource?.embedHtml || resource)) ||
    card.accessible === false ||
    layout.toLowerCase() === CARD_LAYOUT.TILE ||
    isBigCardLMSContent(layout, card) || // show thumbnail for LMS bigcard content
    showInline === false // if the launchInline key is present in card and its value is false then we should not render embedHtml
  )
    return false;

  if (embedHtml) {
    const srcSplit = embedHtml.replace(/<script.*script>/, '').split('src=');
    const isVimeoVideo = validateIfVimeoVideo(resource);

    if (srcSplit.length == 2) {
      const srcContainedString = srcSplit[1];
      const firstCharacter = srcContainedString.charAt(0);
      const indexOfQuotes = srcContainedString
        .substr(1, srcContainedString.length)
        .indexOf(firstCharacter);
      const url = srcContainedString.substr(1, indexOfQuotes);

      if (url) {
        // In some cases this url also does not work since Content Security Policy being set
        const finalUrl = url
          .replace(/'/g, '"')
          .replace(/width=\"[0-9]*\"/g, 'width="100%"')
          .replace(/height=\"[0-9]*\"/g, 'height="294"')
          .replace(/autoplay=[a-z0-9-]*/g, 'autoplay=0')
          .replace('feature=oembed', 'feature=oembed&rel=0&showinfo=0');

        return {
          configData: true,
          url: finalUrl,
          showFullScreenButton: !validateIfYoutubeVideo(resource),
          height: layout === 'Standalone' ? '568' : '305',
          width: layout === 'Standalone' ? '1168' : '100%'
        };
      }
    } else if (isVimeoVideo) {
      if (layout === 'featured') return false;
      // Handling Vimeo Video with custom styles
      const finalUrl = embedHtml
        .replace(/'/g, '"')
        .replace(/width=\"[0-9]*\"/g, 'width="100%"')
        .replace(/height=\"[0-9]*\"/g, 'height="294"')
        .replace(/autoplay=[a-z0-9-]*/g, 'autoplay=0')
        .replace('feature=oembed', 'feature=oembed&rel=0&showinfo=0');

      return {
        configData: true,
        url: finalUrl,
        showFullScreenButton: false,
        isVimeoVideoWithCustomStyles: true,
        height: layout === 'Standalone' ? '480' : '294'
      };
    }
  }

  return '';
};

export default fetchEmbedHtmlDetails;
