import React, { forwardRef, useState, useCallback, useRef, useMemo, createElement } from 'react';
import { BaseHorizontalCard3Columns } from 'centralized-design-system/src/Card/HorizontalCard/HorizontalCard';
import { translatr, omp, ompLov } from 'centralized-design-system/src/Translatr';
import cn from 'classnames';
import { JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import AspirationsContext from '@pages/TalentMarketplace/shared/AspirationsContext';
import {
  isLovAvailableForOpportunityType,
  MAX_ASPIRATIONAL_ROLES,
  preventReadLiveRegions,
  shouldShowTMJobVacancy
} from '@pages/TalentMarketplace/util';
import { useDispatch, useSelector } from 'react-redux';
import { openOMPShareModal } from '@pages/TalentMarketplace/shared/helpers/helpers';
import { useNavigate } from 'react-router-dom';
import MatchComponent from '@components/MatchComponent';
import { openMatchModal } from '@actions/modalActions';
import './styles.scss';
import TextClamp from 'centralized-design-system/src/TextClamp';
import { LOCATION_USAGE_OPTIONS } from '@pages/TalentMarketplace/helpers';
import {
  CardLocations,
  RelatedSkillsPopover,
  RoleSkillItem,
  SharedBy,
  SharedByUser
} from '../common/partials';
import { Button, ButtonLink } from 'centralized-design-system/src/Buttons';
import { DisplayTag } from 'centralized-design-system/src/Tags';
import { navigateTo } from 'centralized-design-system/src/Card/HorizontalCard/BaseHorizontalCard/hooks';
import { open_v3 } from '@actions/snackBarActions';
import { confirmation } from '@actions/modalActions';
import AspirationalConfirmationModal from '@pages/TalentMarketplace/DetailPage/components/AspirationalConfirmationModal/AspirationalConfirmationModal';
import { IconMove } from './IconMove';
import { generateDirectPath } from '@pages/TalentMarketplace/DevelopmentPlan/utils';
import Portal from 'centralized-design-system/src/Portal';

interface Role {
  id: string;
  title: string;
  aspirational: boolean;
  dismissed: boolean;
  overallScore: number;
  overallScoreStatus: string;
  hasTransitionPlan: boolean;
  hasOpenJobs: boolean;
  skillsGraphScore: number;
  locations: { id: string; name: string }[];
  mode: string;
  level?: { id: string; name: string };
  jobFamily?: { title: string };
  sharedBy?: { user: SharedByUser }[];
  area?: string;
  noOfMoves: number;
}

interface RoleCardProps {
  role: Role;
  onClick?: () => void;
  link?: { to: string; state?: any };
  onAspirationChange?: (
    id: string,
    aspirational: boolean,
    title: string,
    role: any
  ) => Promise<any>;
  onDismiss?: (
    id: string,
    dismissed: boolean,
    details: { title: string; matching_score: number }
  ) => void;
  dismissable?: boolean;
  isCareerPathEnabled?: boolean;
  isDevelopmentPlanEnabled?: boolean;
  isSkillsEnabled?: boolean;
  isMatchingEnabled?: boolean;
  isLocationEnabled?: boolean;
  countries?: { id: string; name: string }[];
  selected?: boolean;
}

interface IAspirationsContext {
  aspirations: Role[];
  isAspirationsLoading: boolean;
  isAspirationsInitialized: boolean;
  isAspirationsInitLoading: boolean;
  isAspirationalRole: (roleId: string) => boolean;
  updateAspirations: () => (
    id: number,
    isAspirational: boolean,
    title: string,
    role: Role
  ) => Promise<Symbol>;
  hasAspirationalRole: () => void;
  getAspirationsResponse: () => void;
}

const addDivider = (value: React.ReactNode, index: number, length: number, container = 'li') => {
  return createElement(  container,  { key: index, className: `items-in-row-${length}` },  <span>{value}</span>,  index < length - 1 && (<span aria-hidden="true"> | </span>)
)};


const HorizontalRoleCard: React.FC<RoleCardProps> = forwardRef(
  (
    {
      role,
      onClick,
      link,
      onAspirationChange,
      onDismiss,
      dismissable,
      countries = [],
      isCareerPathEnabled = false,
      isDevelopmentPlanEnabled = false,
      isLocationEnabled = false,
      isSkillsEnabled = false,
      isMatchingEnabled = false,
      selected = false
    },
    ref
  ) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const baseHorizontalCardRef = useRef(null);
    const menuRef = baseHorizontalCardRef?.current?.menuRef;
    const [relatedSkillsAnchorEl, setRelatedSkillsAnchorEl] = useState(null);
    const [isAspirationalConfirmationModalOpen, setIsAspirationalConfirmationModalOpen] = useState(
      false
    );
    const [roleSkillsById, setRoleSkillsById] = React.useState<{
      [roleId: string]: RoleSkillItem[];
    }>({});
    const currentUser = useSelector((state: any) => state.user);
    const isCurrentRole = currentUser?.get('jobFamily')?.get('roleId') === role.id;
    const type = JOB_TYPE.ROLE;

    const { aspirations: aspirationalRoles, isAspirationalRole } = React.useContext(
      AspirationsContext
    ) as IAspirationsContext;
    const roleWithSavedPlan = isDevelopmentPlanEnabled && role?.hasTransitionPlan;
    const isAspirational = isAspirationalRole(role.id);

    const directPath = useMemo(() => generateDirectPath(currentUser?.jobFamily, role), [
      role,
      currentUser?.jobFamily
    ]);

    const getMoveLabel = (moveNr: number): string => {
      return moveNr === 1
        ? translatr('web.talentmarketplace.main', 'Move', { cnt: moveNr })
        : translatr('web.talentmarketplace.main', 'Moves', { cnt: moveNr });
    };

    const beforeHandleToggleAspirationalRole = e => {
      e.stopPropagation();
      if (roleWithSavedPlan && isAspirational) {
        dispatch(
          confirmation(
            translatr('web.common.main', 'RemoveAspirationalRoleConfirmationConfigurable', {
              tm_aspirational_role: omp('tm_tm_aspirational_role')
            }),
            translatr('web.common.main', 'RemoveAspirationalRoleDescriptionConfigurable', {
              tm_aspirational_role: omp('tm_tm_aspirational_role')
            }),
            () => handleToggleAspirationRole(),
            false,
            translatr('web.common.main', 'Remove'),
            false,
            false,
            translatr('web.common.main', 'Cancel')
          )
        );
      } else {
        handleToggleAspirationRole();
      }
    };

    const handleToggleAspirationRole = useCallback(() => {
      onAspirationChange(role.id, !isAspirational, role.title, role)
        .then(() => {
          if (isDevelopmentPlanEnabled && !role.aspirational) {
            setIsAspirationalConfirmationModalOpen(true);
          }

          dispatch(
            open_v3({
              message: translatr(
                'web.common.main',
                !role.aspirational
                  ? 'OpportunityMarkedAsAspirationalConfigurable'
                  : 'OpportunityUnmarkedAsAspirationalConfigurable',
                {
                  opportunity: omp(`tm_job_role`),
                  tm_aspirational_role: omp('tm_tm_aspirational_role')
                }
              ),
              closeHandler: () => {
                menuRef?.current?.focus();
              }
            })
          );
        })
        .catch(error => {
          console.error(`Error in RoleCard.onAspirationChange.func: ${error?.message}`);
        });
    }, [role.aspirational, onAspirationChange]);

    // logic for aspiration change and dismiss need to be added as redux actions
    const menuItems = [
      {
        label: translatr(
          'web.common.main',
          role.aspirational
            ? 'UnMarkOpportunityAsAspirationalConfigurable'
            : 'MarkOpportunityAsAspirationalConfigurable',
          { tm_aspirational_role: omp('tm_tm_aspirational_role') }
        ),
        action: beforeHandleToggleAspirationalRole,
        disabled:
          !role.dismissed &&
          aspirationalRoles.length >= MAX_ASPIRATIONAL_ROLES && !role.aspirational,
        hidden: !onAspirationChange
      },
      {
        label: translatr('web.talentmarketplace.main', 'ViewJobRoleDetails', {
          tm_job_role: omp('tm_job_role')
        }),
        action: () => navigate(link.to, { state: link.state })
      },
      {
        label: translatr('web.common.main', 'Share'),
        action: () =>
          dispatch(
            openOMPShareModal({
              id: role.id,
              type: JOB_TYPE.ROLE,
              preSelectedIndividuals: [],
              openedFromHtmlElement: menuRef?.current
            })
          )
      },
      {
        label: translatr('web.common.main', role.dismissed ? 'Undismiss' : 'Dismiss'),
        action: () =>
          onDismiss(role.id, role.dismissed, {
            title: role.title,
            matching_score: role.overallScore
          }),
        hidden: !(dismissable && !role.aspirational && onDismiss)
      },
      {
        label: translatr('web.talentmarketplace.main', 'ViewActionPlan'),
        action: () => navigate(`/career/plan/${role.id}`),
        hidden: !roleWithSavedPlan
      }
    ];

    const extraAction = role.aspirational
      ? {
          label: translatr('web.common.main', 'OpportunityMarkedAsAspirationalConfigurable', {
            opportunity: omp(`tm_job_role`),
            tm_aspirational_role: omp('tm_tm_aspirational_role')
          }),
          icon: 'icon-bullseye-arrow'
        }
      : role.dismissed && dismissable && !role.aspirational
      ? {
          label: translatr('web.common.main', 'Dismissed'),
          icon: 'icon-ban'
        }
      : null;

    const getRoleCardMetadata = () =>
      [
        isLocationEnabled && role.locations?.length > 0 && (
          <CardLocations
            locations={role.locations}
            type={type}
            visibilityContext={LOCATION_USAGE_OPTIONS.JOB_ROLE_CARD}
            countries={countries}
            mode={role.mode}
            id={role.id}
            entity={role.title}
          />
        ),
        role.level && role.level.id && isLovAvailableForOpportunityType('level', type) && (
          <TextClamp Component="span" line={1}>
            {ompLov('level', role.level?.id)}
          </TextClamp>
        ),
        (role.jobFamily?.title || role.area) && (
          <TextClamp Component="span" line={1}>
            {role.jobFamily.title || role.area}
          </TextClamp>
        )
      ].filter(Boolean);

    const getRoleCardButtons = () =>
      [
        <Button
          id={`aspirational-button-${role.id}`}
          aria-controls={`related-skills-popover-${role.id}`}
          color="secondary"
          variant="borderless"
          size="medium"
          padding="xsmall"
          onClick={e => setRelatedSkillsAnchorEl(e.currentTarget)}
          aria-expanded={Boolean(relatedSkillsAnchorEl)}
        >
          {translatr('web.talentmarketplace.main', 'RelatedSkills')}
        </Button>,
        roleWithSavedPlan && (
          <div>
            <ButtonLink
              id={`action-plan-button-${role.id}`}
              color="secondary"
              variant="borderless"
              size="medium"
              padding="xsmall"
              to={`/career/plan/${role.id}`}
              aria-labelledby={`action-plan-button-${role.id} ed-h-card-title-${role.id}`}
            >
              {translatr('web.talentmarketplace.main', 'ActionPlan')}
            </ButtonLink>
            <DisplayTag
              tagName={translatr('web.talentmarketplace.main', 'Phase', { phaseNmb: 1 })}
            />
          </div>
        )
      ].filter(Boolean);

    const getRoleCardItems = () => {
      const roleCardMetadata = getRoleCardMetadata();
      const buttons = getRoleCardButtons();
      return [
        isMatchingEnabled && (
          <MatchComponent
            score={role.overallScore}
            scoreStatus={role.overallScoreStatus}
            wrapperClass={'h-role-card__matching'}
            compact
            openModalFn={e => {
              e.stopPropagation();
              dispatch(
                openMatchModal(
                  role.id,
                  type,
                  { overallScore: role.overallScore },
                  isCareerPathEnabled,
                  e.target
                )
              );
            }}
            roleTitleId={`ed-h-card-title-${role.id}`}
          />
        ),
        role.sharedBy?.length > 0 && <SharedBy sharedBys={role.sharedBy} />,

        roleCardMetadata.length > 0 && (
          <ul className="h-role-card__row-items">
            {roleCardMetadata.map((it, idx) => addDivider(it, idx, roleCardMetadata.length))}
          </ul>
        ),

        isSkillsEnabled && buttons.length > 0 && (
          <div className="h-role-card__row-items">
            {buttons.map((it, idx) => addDivider(it, idx, buttons.length, 'div'))}
          </div>
        )
      ]
        .filter(Boolean)
        .map((item, index) => (
          <div key={index} className="h-role-card__row">
            {item}
          </div>
        ));
    };
    const handleKeyDown = (
      ev: React.KeyboardEvent,
      action: (ev: React.MouseEvent | React.KeyboardEvent) => void
    ) => {
      if (ev.key === 'Enter' || ev.key === ' ') {
        if (document.activeElement === ev.currentTarget) {
          navigateTo(ev, action);
        }
      }
    };

    return (
      <div
        className={cn('h-role-card', { 'h-role-card__selected': selected })}
        ref={ref as React.Ref<HTMLDivElement>}
      >
        <BaseHorizontalCard3Columns
          ref={baseHorizontalCardRef}
          id={role.id}
          title={role.title}
          link={link}
          flags={[
            role.noOfMoves && {
              color: 'info',
              label: getMoveLabel(role.noOfMoves),
              customIcon: <IconMove />
            },
            role.hasOpenJobs &&
              shouldShowTMJobVacancy() && {
                color: 'info',
                label: omp(`tm_job_vacancy`),
                icon: 'icon-suitcase'
              }
          ].filter(Boolean)}
          menu={!isCurrentRole ? menuItems : []}
          extraAction={extraAction}
          isCurrent={selected}
          onClick={onClick}
          onKeyDown={(ev: React.KeyboardEvent) => handleKeyDown(ev, onClick)}
          tabIndex={0}
        >
          {getRoleCardItems()}
        </BaseHorizontalCard3Columns>
        {relatedSkillsAnchorEl && (
          <RelatedSkillsPopover
            role={role}
            skills={roleSkillsById[role.id]}
            onLoaded={(skills: RoleSkillItem[]) => {
              setRoleSkillsById(prevState => ({
                ...prevState,
                [role.id]: skills
              }));
            }}
            onClose={() => {
              preventReadLiveRegions();
              setRelatedSkillsAnchorEl(null);
            }}
            anchorEl={relatedSkillsAnchorEl}
          />
        )}
        {isDevelopmentPlanEnabled && isAspirationalConfirmationModalOpen && (
          <Portal>
            <AspirationalConfirmationModal
              roleId={role.id}
              title={role.title}
              selectedPathData={directPath}
              closeModal={setIsAspirationalConfirmationModalOpen}
            />
          </Portal>
        )}
      </div>
    );
  }
);

export default HorizontalRoleCard;
