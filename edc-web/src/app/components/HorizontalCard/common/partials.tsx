import React, { useState, useEffect, useRef } from 'react';
import OwnersList from '@pages/Projects/shared/OwnersList';
import { ompLov, translatr } from 'centralized-design-system/src/Translatr';
import ConditionalWrapper from '@components/ConditionalWrapper';
import TextClamp from 'centralized-design-system/src/TextClamp';
import JobLocation from '@components/JobCard/JobLocation';
import { isLovAvailableForOpportunityType } from '@pages/TalentMarketplace/util';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import Popover from '@mui/material/Popover';
import Skeleton from 'centralized-design-system/src/SkeletonAnimations/Skeleton';
import './RelatedSkillsPopover.scss';
import cn from 'classnames';
import Tooltip from 'centralized-design-system/src/Tooltip';
import { getOpportunitySkills } from 'edc-web-sdk/requests/extOpportunities';
import { CAPABILITY_STATUS, JOB_TYPE } from 'edc-web-sdk/requests/careerOportunities.v2';
import { ClickableTag, DisplayTag } from 'centralized-design-system/src/Tags';
import { isMfeEnabled, openSkillFlyout, getSkillId } from '@components/MfeSkillsFlyout';

export interface SharedByUser {
  id: number;
  fullName: string;
  handle: string;
  avatarimages: {
    tiny: string;
  };
}

export const SharedBy: React.FC<{ sharedBys: { user: SharedByUser }[] }> = ({ sharedBys }) => {
  if (!sharedBys || sharedBys.length === 0) {
    return null;
  }
  return (
    <OwnersList
      label={translatr('web.common.main', 'SharedBy')}
      owners={sharedBys?.map(({ user }) => ({
        id: user?.id.toString(),
        fullName: user?.fullName,
        handle: user?.handle,
        avatarUrl: user?.avatarimages?.tiny
      }))}
      avatarSize="small"
    />
  );
};

export const CardLocations: React.FC<{
  locations: { id: string; name: string }[];
  type: string;
  countries: any[];
  mode: string;
  id?: string;
  visibilityContext?: any;
  entity?: string;
}> = ({ locations, type, countries, mode, id, visibilityContext, entity }) => {
  if (!locations || locations.length === 0) {
    return null;
  }
  return (
    <>
      <span id={`location-title-${id}`} className="sr-only">
        {translatr('web.common.main', 'Location')}
      </span>
      <ConditionalWrapper
        condition={locations.length === 1}
        wrapper={children => <TextClamp>{children}</TextClamp>}
        defaultWrapper={children => <>{children}</>}
      >
        <JobLocation
          locations={locations}
          visibilityContext={visibilityContext}
          jobId={id}
          countries={countries}
          type={type}
          lines={1}
          titleId={`ed-h-card-title-${id}`}
          locationIconId={`location-title-${id}`}
        />
        {mode && isLovAvailableForOpportunityType('workplace_model', type, mode) && (
          <>
            <span> | </span>
            <span
              dangerouslySetInnerHTML={{ __html: safeRender(ompLov('workplace_model', mode)) }}
            />
          </>
        )}
      </ConditionalWrapper>
    </>
  );
};

export type RoleSkillItem = {
  id: string;
  label: string;
  isChecked?: boolean;
  externalData: Array<{
    id: string;
    external_source: string;
  }>;
};

export const RelatedSkillsPopover: React.FC<{
  anchorEl: any;
  role: { id: string; skillsGraphScore: number; overallScore: number };
  skills?: RoleSkillItem[];
  onClose: () => void;
  onLoaded?: (skills: RoleSkillItem[]) => void;
}> = ({ role, anchorEl, skills: memoizedSkills = [], onClose, onLoaded }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [skills, setSkills] = useState(memoizedSkills);
  const isSkillFlyout = (skill: any) => (isMfeEnabled() && getSkillId(skill));
  const tooltipMsg = translatr('web.talentmarketplace.main', 'YouMeetSkillLevelRequirements');
  const listRef = useRef(null);
  const timeoutId = useRef(null);

  const getRelatedSkills = async () => {
    setIsLoading(true);
    const response: {
      type: string;
      name: string;
      status: {
        value: string;
      };
      valid: boolean;
      externalData: Array<{
        id: string;
        external_source: string;
      }>;
    }[] = await getOpportunitySkills(role.id, JOB_TYPE.ROLE);
    const priority: { [key: string]: number } = {
      MATCH: 1,
      NOT_MATCH: 2,
      UNKNOWN: 3
    };
    const mapppedResponse = response
      .sort((a, b) => priority[a.status?.value] - priority[b.status?.value])
      ?.map?.(({ name, status, externalData }, i) => ({
        id: `${i}`,
        label: name,
        isChecked: status?.value === 'MATCH',
        externalData
      }));
    onLoaded && onLoaded(mapppedResponse);
    setSkills(mapppedResponse);
    setIsLoading(false);
    focusFirstListtItem();
  };

  useEffect(() => {
    if (Boolean(anchorEl) && !memoizedSkills?.length) {
      getRelatedSkills();
    } else {
      focusFirstListtItem(200);
    }
    return () => {
      clearTimeout(timeoutId.current);
    };
  }, [anchorEl, role.id]);

  const focusFirstListtItem = (time: number = 0) => {
    timeoutId.current = setTimeout(() => {
      const list = listRef.current;
      if (list) {
        const firstItem = list.querySelector(
          '[aria-describedby="related-skills__tooltip-to-remove"][tabindex="0"]'
        );
        firstItem && firstItem.focus();
      }
    }, time);
  };

  return (
    <Popover
      id={`related-skills-popover-${role.id}`}
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'center',
        horizontal: 'center'
      }}
      transformOrigin={{
        vertical: 'center',
        horizontal: 'left'
      }}
      disablePortal
      disableScrollLock
      slotProps={{
        paper: {
          elevation: 2,
          style: {
            overflow: 'visible'
          }
        }
      }}
    >
      <div className="related-skills-popover__container ed-ui" role="dialog">
        <div className="related-skills">
          <h2 className="related-skills__title">
            {translatr('web.talentmarketplace.main', 'RelatedSkills')}
          </h2>
          {isLoading && <Skeleton height={50} />}
          {!isLoading && (
            <>
              <ul className="related-skills__skills" ref={listRef}>
                {skills.map(skill => (
                  <li key={skill.id}>
                    <Tooltip
                      message={tooltipMsg}
                      pos="top"
                      hide={!skill.isChecked}
                      id={`tooltipCard-${skill.id}`}
                      {...(skill.isChecked && !isSkillFlyout(skill) ? {
                        tabIndex: '0',
                        ariaDescribedby: "related-skills__tooltip-to-remove"
                      } : {})}
                    >
                      {isSkillFlyout(skill) ? (
                        <ClickableTag
                          id={skill.id}
                          key={skill.id}
                          callback={() => {
                            openSkillFlyout(skill);
                          }}
                          tagName={
                            <div
                              className={cn('related-skills__tag', {
                                'related-skills__tag--success': skill.isChecked
                              })}
                            >
                              {skill.isChecked && <i className="icon-check" />}
                              {skill.label}
                            </div>
                          }
                          aria-describedby={skill.isChecked ? "related-skills__tooltip-to-remove" : ""}
                        />
                      ) : (
                        <DisplayTag
                          id={skill.id}
                          key={skill.id}
                          tagName={
                            <div
                              className={cn('related-skills__tag', {
                                'related-skills__tag--success': skill.isChecked
                              })}
                            >
                              {skill.isChecked && <i className="icon-check" />}
                              {skill.label}
                            </div>
                          }
                        />
                      )}
                    </Tooltip>
                  </li>
                ))}
              </ul>
              <div
                id="related-skills__tooltip-to-remove"
                className="sr-only"
                dangerouslySetInnerHTML={{ __html: safeRender(tooltipMsg) }}
                aria-hidden="true"
              />
            </>
          )}
          {<div className="odp-arrow"></div>}
        </div>
      </div>
    </Popover>
  );
};
